const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');

const config = require('./config');
const { generateActivationKey, decryptActivationKey, createShortCode } = require('./utils/encryption');

// Create Express app
const app = express();

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    // Allow localhost and admin panel
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8081',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
      'http://127.0.0.1:8081'
    ];
    
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    // Allow any origin in development
    if (config.NODE_ENV === 'development') {
      return callback(null, true);
    }
    
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-admin-token', 'x-session-id']
}));

// Compression middleware
app.use(compression());

// Logging middleware
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'NSO Backend API is running (Test Mode)',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.NODE_ENV,
    mode: 'test'
  });
});

// Mock data
const mockUsers = [];
const mockActivationKeys = [];

// Test activation endpoint
app.post('/api/v1/auth/activate', (req, res) => {
  try {
    const { activationKey, deviceInfo } = req.body;
    
    console.log('Activation request:', { activationKey: activationKey?.substring(0, 10) + '...', deviceInfo });
    
    if (!activationKey || !deviceInfo || !deviceInfo.deviceId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        code: 'MISSING_FIELDS'
      });
    }

    // Decrypt and validate activation key
    const keyData = decryptActivationKey(activationKey);
    
    if (!keyData.isValid) {
      console.log('Invalid activation key:', keyData.error);
      
      return res.status(400).json({
        success: false,
        error: keyData.error || 'Invalid activation key',
        code: keyData.isExpired ? 'KEY_EXPIRED' : 'INVALID_ACTIVATION_KEY'
      });
    }

    // Verify device ID matches
    if (keyData.deviceId !== deviceInfo.deviceId) {
      console.log('Device ID mismatch:', {
        expected: keyData.deviceId,
        actual: deviceInfo.deviceId
      });
      
      return res.status(400).json({
        success: false,
        error: 'Device ID mismatch',
        code: 'DEVICE_MISMATCH'
      });
    }

    // Check if device is already activated
    const existingUser = mockUsers.find(u => u.deviceId === deviceInfo.deviceId);
    if (existingUser && existingUser.isActivated) {
      return res.status(400).json({
        success: false,
        error: 'Device already activated',
        code: 'DEVICE_ALREADY_ACTIVATED'
      });
    }

    // Create mock user
    const user = {
      userId: keyData.userId,
      fullName: `User ${keyData.userId}`,
      email: `${keyData.userId}@temp.nso.gov.ng`,
      role: keyData.role,
      facility: keyData.facility,
      deviceId: deviceInfo.deviceId,
      isActivated: true,
      activatedAt: new Date().toISOString()
    };

    mockUsers.push(user);

    // Generate mock JWT token
    const token = 'mock-jwt-token-' + Date.now();

    console.log('Device activated successfully:', {
      userId: user.userId,
      deviceId: user.deviceId,
      facility: user.facility
    });

    res.status(200).json({
      success: true,
      message: 'Device activated successfully',
      data: {
        user: {
          userId: user.userId,
          fullName: user.fullName,
          role: user.role,
          facility: user.facility,
          deviceId: user.deviceId
        },
        token,
        expiresIn: '7d',
        keyExpiresAt: keyData.expiresAt,
        remainingDays: keyData.remainingDays
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Activation error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// Test admin endpoint to create users
app.post('/api/v1/admin/users', (req, res) => {
  try {
    const {
      fullName,
      email,
      role,
      facility,
      state,
      contactInfo,
      deviceId,
      validityMonths = 12,
      notes = ''
    } = req.body;

    // Validate required fields
    if (!fullName || !email || !role || !facility || !state || !contactInfo || !deviceId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        code: 'MISSING_FIELDS'
      });
    }

    // Generate unique user ID and key ID
    const userId = `NSO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const keyId = `KEY_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;

    // Generate activation key
    const activationKeyData = {
      userId,
      deviceId,
      role,
      facility,
      validityMonths: parseInt(validityMonths)
    };

    const encryptedKey = generateActivationKey(activationKeyData);
    const shortCode = createShortCode(encryptedKey);

    // Store mock data
    const activationKey = {
      keyId,
      activationKey: encryptedKey,
      shortCode,
      userId,
      deviceId,
      role,
      facility,
      state,
      validityMonths: parseInt(validityMonths),
      expiresAt: new Date(Date.now() + parseInt(validityMonths) * 30 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString()
    };

    mockActivationKeys.push(activationKey);

    console.log('Admin created user:', {
      userId,
      email,
      facility,
      keyId
    });

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        user: {
          userId,
          fullName,
          email,
          role,
          facility,
          state,
          deviceId
        },
        activationKey: {
          keyId,
          activationKey: encryptedKey,
          shortCode,
          expiresAt: activationKey.expiresAt,
          validityMonths: activationKey.validityMonths
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Admin create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    code: 'ENDPOINT_NOT_FOUND',
    path: req.originalUrl,
    method: req.method
  });
});

// Start server
const PORT = config.PORT;
const server = app.listen(PORT, () => {
  console.log(`🚀 NSO Backend Test Server running on port ${PORT}`);
  console.log(`📍 Environment: ${config.NODE_ENV}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`⚡ Mode: Test (No Database Required)`);
});

module.exports = app;
