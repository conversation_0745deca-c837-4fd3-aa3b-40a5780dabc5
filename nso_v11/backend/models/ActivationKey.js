const mongoose = require('mongoose');

const activationKeySchema = new mongoose.Schema({
  // Key Information
  keyId: {
    type: String,
    required: true,
    unique: true
  },
  activationKey: {
    type: String,
    required: true,
    unique: true
  },
  shortCode: {
    type: String,
    required: true,
    unique: true
  },
  
  // User Information
  userId: {
    type: String,
    required: true,
    index: true
  },
  deviceId: {
    type: String,
    required: true,
    index: true
  },
  
  // Key Metadata
  role: {
    type: String,
    required: true,
    enum: ['Doctor', 'Nurse', 'Medical Officer', 'Healthcare Worker', 'Administrator']
  },
  facility: {
    type: String,
    required: true,
    trim: true
  },
  state: {
    type: String,
    required: true,
    trim: true
  },
  
  // Validity Information
  validityMonths: {
    type: Number,
    required: true,
    min: 1,
    max: 60 // Maximum 5 years
  },
  issuedAt: {
    type: Date,
    required: true,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    required: true,
    index: true
  },
  
  // Status
  status: {
    type: String,
    enum: ['active', 'used', 'expired', 'revoked'],
    default: 'active',
    index: true
  },
  isUsed: {
    type: Boolean,
    default: false,
    index: true
  },
  usedAt: {
    type: Date
  },
  
  // Admin Information
  createdBy: {
    adminId: {
      type: String,
      required: true
    },
    adminName: {
      type: String,
      required: true
    },
    adminEmail: {
      type: String,
      required: true
    }
  },
  
  // Usage Information
  activationAttempts: [{
    deviceId: String,
    deviceInfo: {
      model: String,
      osVersion: String,
      appVersion: String
    },
    ipAddress: String,
    userAgent: String,
    attemptedAt: {
      type: Date,
      default: Date.now
    },
    success: Boolean,
    errorCode: String,
    errorMessage: String
  }],
  
  // Notes and Comments
  notes: {
    type: String,
    trim: true
  },
  adminNotes: {
    type: String,
    trim: true
  },
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes (unique fields already have indexes automatically)
activationKeySchema.index({ userId: 1, deviceId: 1 });
activationKeySchema.index({ status: 1, expiresAt: 1 });
activationKeySchema.index({ 'createdBy.adminId': 1 });
activationKeySchema.index({ facility: 1, state: 1 });
activationKeySchema.index({ issuedAt: -1 });

// Pre-save middleware
activationKeySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Update status based on expiry and usage
  const now = new Date();
  if (this.expiresAt < now && this.status === 'active') {
    this.status = 'expired';
  }
  if (this.isUsed && this.status === 'active') {
    this.status = 'used';
  }
  
  next();
});

// Instance methods
activationKeySchema.methods.markAsUsed = function(deviceInfo = {}) {
  this.isUsed = true;
  this.usedAt = new Date();
  this.status = 'used';
  
  // Add successful activation attempt
  this.activationAttempts.push({
    deviceId: this.deviceId,
    deviceInfo,
    attemptedAt: new Date(),
    success: true
  });
  
  return this.save();
};

activationKeySchema.methods.addActivationAttempt = function(attemptData) {
  this.activationAttempts.push({
    ...attemptData,
    attemptedAt: new Date()
  });
  return this.save();
};

activationKeySchema.methods.revoke = function(reason = '') {
  this.status = 'revoked';
  this.adminNotes = this.adminNotes ? `${this.adminNotes}\nRevoked: ${reason}` : `Revoked: ${reason}`;
  return this.save();
};

activationKeySchema.methods.isValid = function() {
  const now = new Date();
  return this.status === 'active' && 
         !this.isUsed && 
         this.expiresAt > now;
};

activationKeySchema.methods.getRemainingDays = function() {
  const now = new Date();
  const diffTime = this.expiresAt - now;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Static methods
activationKeySchema.statics.findByKeyId = function(keyId) {
  return this.findOne({ keyId });
};

activationKeySchema.statics.findByActivationKey = function(activationKey) {
  return this.findOne({ activationKey });
};

activationKeySchema.statics.findByShortCode = function(shortCode) {
  return this.findOne({ shortCode: shortCode.toUpperCase() });
};

activationKeySchema.statics.findByUserId = function(userId) {
  return this.find({ userId }).sort({ createdAt: -1 });
};

activationKeySchema.statics.findByDeviceId = function(deviceId) {
  return this.find({ deviceId }).sort({ createdAt: -1 });
};

activationKeySchema.statics.getActiveKeys = function() {
  return this.find({ 
    status: 'active',
    isUsed: false,
    expiresAt: { $gt: new Date() }
  });
};

activationKeySchema.statics.getExpiredKeys = function() {
  return this.find({
    $or: [
      { expiresAt: { $lt: new Date() } },
      { status: 'expired' }
    ]
  });
};

activationKeySchema.statics.getKeysByAdmin = function(adminId) {
  return this.find({ 'createdBy.adminId': adminId }).sort({ createdAt: -1 });
};

activationKeySchema.statics.getKeysByFacility = function(facility) {
  return this.find({ facility }).sort({ createdAt: -1 });
};

activationKeySchema.statics.getUsageStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

module.exports = mongoose.model('ActivationKey', activationKeySchema);
