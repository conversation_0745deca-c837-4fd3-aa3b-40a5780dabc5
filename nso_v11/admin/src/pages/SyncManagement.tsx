import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  Grid,
  LinearProgress,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Sync as SyncIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface SyncOperation {
  id: string;
  type: 'user_data' | 'clinical_records' | 'activities' | 'system_config';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startTime: string;
  endTime?: string;
  recordsProcessed: number;
  totalRecords: number;
  errorCount: number;
  deviceId?: string;
  userId?: string;
}

const mockSyncOperations: SyncOperation[] = [
  {
    id: 'sync_001',
    type: 'user_data',
    status: 'completed',
    progress: 100,
    startTime: '2024-01-15 14:30:00',
    endTime: '2024-01-15 14:32:15',
    recordsProcessed: 1250,
    totalRecords: 1250,
    errorCount: 0,
    deviceId: 'android_1705123456_abc123',
    userId: 'user_001',
  },
  {
    id: 'sync_002',
    type: 'clinical_records',
    status: 'running',
    progress: 65,
    startTime: '2024-01-15 14:35:00',
    recordsProcessed: 650,
    totalRecords: 1000,
    errorCount: 2,
    deviceId: 'ios_1705123789_def456',
    userId: 'user_002',
  },
  {
    id: 'sync_003',
    type: 'activities',
    status: 'failed',
    progress: 45,
    startTime: '2024-01-15 14:20:00',
    endTime: '2024-01-15 14:25:30',
    recordsProcessed: 450,
    totalRecords: 1000,
    errorCount: 15,
    deviceId: 'android_1705124012_ghi789',
    userId: 'user_003',
  },
];

export default function SyncManagement() {
  const [syncOperations, setSyncOperations] = useState<SyncOperation[]>(mockSyncOperations);
  const [selectedOperation, setSelectedOperation] = useState<SyncOperation | null>(null);
  const [openDialog, setOpenDialog] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'info';
      case 'failed':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon />;
      case 'running':
        return <SyncIcon className="animate-spin" />;
      case 'failed':
        return <ErrorIcon />;
      case 'pending':
        return <WarningIcon />;
      default:
        return <SyncIcon />;
    }
  };

  const handleViewDetails = (operation: SyncOperation) => {
    setSelectedOperation(operation);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedOperation(null);
  };

  const syncStats = {
    total: syncOperations.length,
    completed: syncOperations.filter(op => op.status === 'completed').length,
    running: syncOperations.filter(op => op.status === 'running').length,
    failed: syncOperations.filter(op => op.status === 'failed').length,
  };

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
              Sync Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Monitor and manage data synchronization operations
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<PlayArrowIcon />}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                },
              }}
            >
              Start Sync
            </Button>
          </Box>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { label: 'Total Operations', value: syncStats.total, color: '#3b82f6', icon: <SyncIcon /> },
          { label: 'Completed', value: syncStats.completed, color: '#10b981', icon: <CheckCircleIcon /> },
          { label: 'Running', value: syncStats.running, color: '#f59e0b', icon: <SyncIcon /> },
          { label: 'Failed', value: syncStats.failed, color: '#ef4444', icon: <ErrorIcon /> },
        ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={stat.label}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card>
                <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: `${stat.color}20`,
                      color: stat.color,
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: stat.color }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Sync Operations Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <CardContent sx={{ p: 0 }}>
            <Box sx={{ p: 3, borderBottom: '1px solid rgba(0, 0, 0, 0.08)' }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Recent Sync Operations
              </Typography>
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Operation ID</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Progress</TableCell>
                    <TableCell>Records</TableCell>
                    <TableCell>Start Time</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {syncOperations.map((operation) => (
                    <TableRow key={operation.id} hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                          {operation.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={operation.type.replace('_', ' ').toUpperCase()}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(operation.status)}
                          label={operation.status.toUpperCase()}
                          size="small"
                          color={getStatusColor(operation.status) as any}
                          variant="filled"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 120 }}>
                          <LinearProgress
                            variant="determinate"
                            value={operation.progress}
                            sx={{ flex: 1, mr: 1, height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption">
                            {operation.progress}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {operation.recordsProcessed.toLocaleString()} / {operation.totalRecords.toLocaleString()}
                        </Typography>
                        {operation.errorCount > 0 && (
                          <Typography variant="caption" color="error">
                            {operation.errorCount} errors
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(operation.startTime).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleViewDetails(operation)}
                        >
                          <VisibilityIcon />
                        </IconButton>
                        {operation.status === 'running' && (
                          <IconButton size="small" color="error">
                            <StopIcon />
                          </IconButton>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </motion.div>

      {/* Operation Details Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Sync Operation Details
        </DialogTitle>
        <DialogContent>
          {selectedOperation && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Operation ID
                  </Typography>
                  <Typography variant="body1" sx={{ fontFamily: 'monospace', mb: 2 }}>
                    {selectedOperation.id}
                  </Typography>
                  
                  <Typography variant="subtitle2" color="text.secondary">
                    Type
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedOperation.type.replace('_', ' ').toUpperCase()}
                  </Typography>
                  
                  <Typography variant="subtitle2" color="text.secondary">
                    Status
                  </Typography>
                  <Chip
                    icon={getStatusIcon(selectedOperation.status)}
                    label={selectedOperation.status.toUpperCase()}
                    color={getStatusColor(selectedOperation.status) as any}
                    sx={{ mb: 2 }}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Progress
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LinearProgress
                      variant="determinate"
                      value={selectedOperation.progress}
                      sx={{ flex: 1, mr: 2, height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="body1">
                      {selectedOperation.progress}%
                    </Typography>
                  </Box>
                  
                  <Typography variant="subtitle2" color="text.secondary">
                    Records Processed
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedOperation.recordsProcessed.toLocaleString()} / {selectedOperation.totalRecords.toLocaleString()}
                  </Typography>
                  
                  <Typography variant="subtitle2" color="text.secondary">
                    Error Count
                  </Typography>
                  <Typography variant="body1" color={selectedOperation.errorCount > 0 ? 'error' : 'text.primary'}>
                    {selectedOperation.errorCount}
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Timeline
                  </Typography>
                  <Typography variant="body2">
                    Started: {new Date(selectedOperation.startTime).toLocaleString()}
                  </Typography>
                  {selectedOperation.endTime && (
                    <Typography variant="body2">
                      Ended: {new Date(selectedOperation.endTime).toLocaleString()}
                    </Typography>
                  )}
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
