# NSO Mobile App Backend

A comprehensive Node.js/Express backend system for the NSO (Nigerian Standard Organization) mobile application, providing data synchronization, user activity tracking, and clinical data management.

## Features

### 🔐 Authentication & Security
- Device-based authentication with activation keys
- JWT token-based authorization
- Role-based access control
- Device validation and activation
- Security event logging

### 📊 Data Synchronization
- Bidirectional data sync (upload/download)
- Conflict resolution strategies
- Batch processing for large datasets
- Retry mechanisms for failed syncs
- Progress tracking and status monitoring

### 📈 User Activity Tracking
- Comprehensive activity logging
- Performance metrics collection
- Error tracking and analytics
- Session management
- Feature usage analytics

### 🏥 Clinical Data Management
- Clinical records storage and retrieval
- Diagnosis management
- Medical data validation
- Offline data support

### 📊 Analytics & Reporting
- Dashboard analytics
- Usage statistics
- Error analytics
- Performance monitoring
- Sync statistics

## API Endpoints

### Authentication
- `POST /api/v1/auth/activate` - Activate device and create user
- `POST /api/v1/auth/login` - Login with device credentials
- `POST /api/v1/auth/logout` - Logout user
- `GET /api/v1/auth/verify` - Verify JWT token

### Data Synchronization
- `POST /api/v1/sync/upload` - Upload data to server
- `GET /api/v1/sync/download` - Download data from server
- `GET /api/v1/sync/status/:syncId` - Get sync status
- `GET /api/v1/sync/history` - Get sync history
- `POST /api/v1/sync/retry/:syncId` - Retry failed sync
- `GET /api/v1/sync/stats` - Get sync statistics

### User Activity
- `POST /api/v1/activity/track` - Track single activity
- `POST /api/v1/activity/batch` - Track multiple activities
- `GET /api/v1/activity/user` - Get user activity history
- `GET /api/v1/activity/session/:sessionId` - Get session activities
- `GET /api/v1/activity/stats` - Get activity statistics
- `GET /api/v1/activity/performance` - Get performance metrics
- `GET /api/v1/activity/errors` - Get error activities

### User Management
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `GET /api/v1/users/preferences` - Get user preferences
- `PUT /api/v1/users/preferences` - Update user preferences

### Analytics
- `GET /api/v1/analytics/dashboard` - Get dashboard analytics
- `GET /api/v1/analytics/usage` - Get usage analytics
- `GET /api/v1/analytics/errors` - Get error analytics

## Installation

1. **Clone the repository**
   ```bash
   cd nso_v11/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment | development |
| `MONGODB_URL` | MongoDB connection string | Required |
| `JWT_SECRET` | JWT signing secret | Required |
| `JWT_EXPIRES_IN` | JWT expiration time | 7d |
| `CORS_ORIGINS` | Allowed CORS origins | localhost:8082 |
| `LOG_LEVEL` | Logging level | info |

## Database Models

### User
- User profile and device information
- Authentication credentials
- Preferences and settings
- Activity tracking

### SyncData
- Synchronization records
- Progress tracking
- Conflict resolution
- Error handling

### UserActivity
- User interaction tracking
- Performance metrics
- Error logging
- Session management

## Security Features

### Device Authentication
- Unique device activation keys
- Device registration and validation
- Single device per user enforcement

### Data Protection
- JWT token-based authentication
- Role-based access control
- Input validation and sanitization
- Rate limiting

### Audit Logging
- Security event logging
- User activity tracking
- Error monitoring
- Performance metrics

## Sync Architecture

### Upload Process
1. Client uploads data with sync metadata
2. Server validates and processes data
3. Conflict resolution if needed
4. Progress tracking and status updates
5. Activity logging

### Download Process
1. Client requests data with filters
2. Server queries and formats data
3. Pagination for large datasets
4. Incremental sync support
5. Status tracking

### Conflict Resolution
- Last-write-wins strategy
- Manual conflict resolution
- Merge strategies for compatible changes
- Conflict logging and reporting

## Monitoring & Analytics

### Performance Monitoring
- Response time tracking
- Memory usage monitoring
- Database query performance
- Error rate monitoring

### User Analytics
- Feature usage statistics
- Session duration tracking
- Error frequency analysis
- Performance metrics

## Development

### Running Tests
```bash
npm test
```

### Linting
```bash
npm run lint
```

### Database Seeding
```bash
npm run seed
```

## Production Deployment

### Prerequisites
- Node.js 16+
- MongoDB 4.4+
- SSL certificate for HTTPS

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure production MongoDB URL
3. Set strong JWT secret
4. Configure CORS origins
5. Set up SSL/TLS

### Monitoring
- Use PM2 for process management
- Set up log rotation
- Monitor database performance
- Set up health checks

## API Documentation

### Request Headers
- `Authorization: Bearer <token>` - JWT token
- `X-Device-ID: <device-id>` - Device identifier
- `X-Session-ID: <session-id>` - Session identifier
- `X-App-Version: <version>` - App version

### Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Operation successful",
  "timestamp": "2024-12-16T10:30:00.000Z"
}
```

### Error Format
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": [],
  "timestamp": "2024-12-16T10:30:00.000Z"
}
```

## Support

For technical support or questions about the NSO backend system, please contact the development team.

## License

This project is proprietary software developed for the Nigerian Standard Organization (NSO).
