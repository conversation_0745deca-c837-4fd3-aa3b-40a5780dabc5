const express = require('express');
const router = express.Router();

const User = require('../models/User');
const UserActivity = require('../models/UserActivity');
const auth = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const logger = require('../utils/logger');

// GET /api/v1/users/profile - Get user profile
router.get('/profile', auth.auth, async (req, res) => {
  try {
    const { userId } = req.user;
    
    const user = await User.findOne({ userId }).select('-passwordHash');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      success: true,
      user,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// PUT /api/v1/users/profile - Update user profile
router.put('/profile', auth.auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const updateData = req.body;
    
    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updateData.userId;
    delete updateData.deviceInfo;
    delete updateData.passwordHash;
    delete updateData.isActive;
    
    const user = await User.findOneAndUpdate(
      { userId },
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select('-passwordHash');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      user,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/users/preferences - Get user preferences
router.get('/preferences', auth.auth, async (req, res) => {
  try {
    const { userId } = req.user;
    
    const user = await User.findOne({ userId }).select('preferences');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      success: true,
      preferences: user.preferences || {},
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Get preferences error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// PUT /api/v1/users/preferences - Update user preferences
router.put('/preferences', auth.auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const preferences = req.body;
    
    const user = await User.findOneAndUpdate(
      { userId },
      { preferences, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select('preferences');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Preferences updated successfully',
      preferences: user.preferences,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Update preferences error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

module.exports = router;
