import {
    Add as AddIcon,
    Block as BlockIcon,
    Delete as DeleteIcon,
    Edit as EditIcon,
    Map as MapIcon,
    More<PERSON><PERSON> as MoreVertIcon,
    Person as PersonIcon,
    Search as SearchIcon,
    TableChart as TableIcon
} from '@mui/icons-material';
import {
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    InputAdornment,
    InputLabel,
    Menu,
    MenuItem,
    Select,
    Tab,
    Tabs,
    TextField,
    Typography,
} from '@mui/material';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { motion } from 'framer-motion';
import React, { useEffect, useState } from 'react';
import UserMap from '../components/common/UserMap';
import UserForm from '../components/forms/UserForm';
import { apiService, User as ApiUser, CreateUserRequest } from '../services/api';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  facility: string;
  state: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: string;
  deviceId: string;
  registeredAt: string;
  avatar?: string;
  latitude?: number;
  longitude?: number;
  activationKeyInfo?: {
    keyId: string;
    shortCode: string;
    status: string;
    expiresAt: string;
    remainingDays: number;
    createdBy: {
      adminId: string;
      adminName: string;
      adminEmail: string;
    };
  };
}

const mockUsers: User[] = [
  {
    id: '1',
    name: 'Dr. Sarah Johnson',
    email: '<EMAIL>',
    role: 'Doctor',
    facility: 'General Hospital Lagos',
    state: 'Lagos',
    status: 'active',
    lastLogin: '2024-01-15 14:30',
    deviceId: 'android_1705123456_abc123',
    registeredAt: '2024-01-10',
    latitude: 6.5244,
    longitude: 3.3792,
  },
  {
    id: '2',
    name: 'Nurse Mary Wilson',
    email: '<EMAIL>',
    role: 'Nurse',
    facility: 'Primary Health Center',
    state: 'Abuja',
    status: 'active',
    lastLogin: '2024-01-15 12:15',
    deviceId: 'ios_1705123789_def456',
    registeredAt: '2024-01-08',
    latitude: 9.0579,
    longitude: 7.4951,
  },
  {
    id: '3',
    name: 'Dr. Michael Brown',
    email: '<EMAIL>',
    role: 'Medical Officer',
    facility: 'State Hospital',
    state: 'Kano',
    status: 'inactive',
    lastLogin: '2024-01-12 09:45',
    deviceId: 'android_1705124012_ghi789',
    registeredAt: '2024-01-05',
    latitude: 11.9804,
    longitude: 8.5201,
  },
  {
    id: '4',
    name: 'Dr. Amina Hassan',
    email: '<EMAIL>',
    role: 'Specialist',
    facility: 'University Teaching Hospital',
    state: 'Kaduna',
    status: 'active',
    lastLogin: '2024-01-15 16:20',
    deviceId: 'android_1705125678_jkl012',
    registeredAt: '2024-01-03',
    latitude: 10.5105,
    longitude: 7.4165,
  },
  {
    id: '5',
    name: 'Nurse John Okafor',
    email: '<EMAIL>',
    role: 'Nurse',
    facility: 'Community Health Center',
    state: 'Enugu',
    status: 'suspended',
    lastLogin: '2024-01-10 11:30',
    deviceId: 'ios_1705126789_mno345',
    registeredAt: '2024-01-01',
    latitude: 6.4403,
    longitude: 7.4966,
  },
  {
    id: '6',
    name: 'Dr. Fatima Bello',
    email: '<EMAIL>',
    role: 'Pediatrician',
    facility: 'Children Hospital',
    state: 'Katsina',
    status: 'active',
    lastLogin: '2024-01-15 13:45',
    deviceId: 'android_1705127890_pqr678',
    registeredAt: '2023-12-28',
    latitude: 12.9908,
    longitude: 7.6018,
  },
];

export default function Users() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState(0);
  const [viewMode, setViewMode] = useState<'table' | 'map'>('table');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<'view' | 'edit' | 'add'>('view');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Fetch users data
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getUsers({
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm,
        status: selectedTab === 0 ? 'all' : selectedTab === 1 ? 'active' : 'inactive'
      });

      if (response.success) {
        // Transform API data to match our User interface
        const transformedUsers = response.data.users.map((apiUser: ApiUser) => ({
          id: apiUser.userId,
          name: apiUser.fullName,
          email: apiUser.email,
          role: apiUser.role,
          facility: apiUser.facility,
          state: apiUser.state,
          status: apiUser.isActive && apiUser.deviceInfo.isActivated ? 'active' : 'inactive',
          lastLogin: apiUser.deviceInfo.activatedAt || new Date().toISOString(),
          deviceId: apiUser.deviceInfo.deviceId,
          registeredAt: apiUser.registeredAt || new Date().toISOString(),
          latitude: mockUsers.find(u => u.facility === apiUser.facility)?.latitude,
          longitude: mockUsers.find(u => u.facility === apiUser.facility)?.longitude,
          activationKeyInfo: apiUser.activationKeyInfo
        }));

        setUsers(transformedUsers);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total,
          pages: response.data.pagination.pages
        }));
      } else {
        setError('Failed to fetch users');
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to fetch users. Using mock data.');
      // Fallback to mock data
      setUsers(mockUsers);
    } finally {
      setLoading(false);
    }
  };

  // Load users on component mount and when dependencies change
  useEffect(() => {
    fetchUsers();
  }, [pagination.page, pagination.limit, searchTerm, selectedTab]);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, user: User) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const handleOpenDialog = (type: 'view' | 'edit' | 'add', user?: User) => {
    setDialogType(type);
    setSelectedUser(user || null);
    setOpenDialog(true);
    handleMenuClose();
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedUser(null);
  };

  const handleCreateUser = async (userData: CreateUserRequest) => {
    try {
      setLoading(true);
      const response = await apiService.createUser(userData);

      if (response.success) {
        // Show success message with activation key
        alert(`User created successfully!\n\nActivation Key: ${response.data.activationKey.activationKey}\nShort Code: ${response.data.activationKey.shortCode}\nExpires: ${new Date(response.data.activationKey.expiresAt).toLocaleDateString()}`);

        // Refresh users list
        await fetchUsers();
        setOpenDialog(false);
      } else {
        alert('Failed to create user: ' + response.error);
      }
    } catch (error) {
      console.error('Error creating user:', error);
      alert('Failed to create user. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'suspended':
        return 'error';
      default:
        return 'default';
    }
  };

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.facility.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const columns: GridColDef[] = [
    {
      field: 'user',
      headerName: 'User',
      width: 250,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'primary.main',
              mr: 2,
              fontSize: '0.875rem',
            }}
          >
            {params.row.name.charAt(0)}
          </Avatar>
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>
              {params.row.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {params.row.email}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'role',
      headerName: 'Role',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value}
          size="small"
          variant="outlined"
          color="primary"
        />
      ),
    },
    {
      field: 'facility',
      headerName: 'Facility',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Box>
          <Typography variant="body2">{params.value}</Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.state}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value}
          size="small"
          color={getStatusColor(params.value) as any}
          variant="filled"
        />
      ),
    },
    {
      field: 'lastLogin',
      headerName: 'Last Login',
      width: 150,
      renderCell: (params: GridRenderCellParams) => (
        <Typography variant="body2">
          {new Date(params.value).toLocaleDateString()}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <IconButton
          onClick={(e) => handleMenuOpen(e, params.row)}
          size="small"
        >
          <MoreVertIcon />
        </IconButton>
      ),
    },
  ];

  const tabLabels = ['All Users', 'Active', 'Inactive', 'Suspended'];

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
              User Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage healthcare professionals and their access
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog('add')}
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              },
            }}
          >
            Add User
          </Button>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { label: 'Total Users', value: users.length, color: '#3b82f6' },
          { label: 'Active Users', value: users.filter(u => u.status === 'active').length, color: '#10b981' },
          { label: 'Inactive Users', value: users.filter(u => u.status === 'inactive').length, color: '#f59e0b' },
          { label: 'Suspended Users', value: users.filter(u => u.status === 'suspended').length, color: '#ef4444' },
        ].map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={stat.label}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: stat.color, mb: 1 }}>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.label}
                  </Typography>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <CardContent sx={{ p: 0 }}>
            {/* Toolbar */}
            <Box sx={{ p: 3, borderBottom: '1px solid rgba(0, 0, 0, 0.08)' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <TextField
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  sx={{ width: 300 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />

                {/* View Toggle */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant={viewMode === 'table' ? 'contained' : 'outlined'}
                    startIcon={<TableIcon />}
                    onClick={() => setViewMode('table')}
                    size="small"
                  >
                    Table View
                  </Button>
                  <Button
                    variant={viewMode === 'map' ? 'contained' : 'outlined'}
                    startIcon={<MapIcon />}
                    onClick={() => setViewMode('map')}
                    size="small"
                  >
                    Map View
                  </Button>
                </Box>
              </Box>

              {/* Tabs */}
              <Tabs
                value={selectedTab}
                onChange={(_, newValue) => setSelectedTab(newValue)}
                sx={{ mt: 2 }}
              >
                {tabLabels.map((label, index) => (
                  <Tab key={index} label={label} />
                ))}
              </Tabs>
            </Box>

            {/* Content Area */}
            {viewMode === 'table' ? (
              /* Data Grid */
              <Box sx={{ height: 600 }}>
                <DataGrid
                  rows={filteredUsers}
                  columns={columns}
                  initialState={{
                    pagination: {
                      paginationModel: { pageSize: 10 },
                    },
                  }}
                  pageSizeOptions={[10, 25, 50]}
                  disableRowSelectionOnClick
                  sx={{
                    border: 'none',
                    '& .MuiDataGrid-cell': {
                      borderBottom: '1px solid rgba(0, 0, 0, 0.04)',
                    },
                    '& .MuiDataGrid-columnHeaders': {
                      bgcolor: 'rgba(0, 0, 0, 0.02)',
                      borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
                    },
                  }}
                />
              </Box>
            ) : (
              /* Map View */
              <Box sx={{ height: 600, p: 2 }}>
                <UserMap users={filteredUsers} height="100%" />
              </Box>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleOpenDialog('view', selectedUser!)}>
          <PersonIcon sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => handleOpenDialog('edit', selectedUser!)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit User
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <BlockIcon sx={{ mr: 1 }} />
          Suspend User
        </MenuItem>
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete User
        </MenuItem>
      </Menu>

      {/* User Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogType === 'add' ? 'Add New User' : 
           dialogType === 'edit' ? 'Edit User' : 'User Details'}
        </DialogTitle>
        <DialogContent>
          {dialogType === 'add' ? (
            <UserForm
              onSubmit={handleCreateUser}
              onCancel={handleCloseDialog}
              loading={loading}
            />
          ) : selectedUser && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    value={selectedUser.name}
                    disabled={dialogType === 'view'}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    value={selectedUser.email}
                    disabled={dialogType === 'view'}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth disabled={dialogType === 'view'}>
                    <InputLabel>Role</InputLabel>
                    <Select value={selectedUser.role} label="Role">
                      <MenuItem value="Doctor">Doctor</MenuItem>
                      <MenuItem value="Nurse">Nurse</MenuItem>
                      <MenuItem value="Medical Officer">Medical Officer</MenuItem>
                      <MenuItem value="Healthcare Worker">Healthcare Worker</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Facility"
                    value={selectedUser.facility}
                    disabled={dialogType === 'view'}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="State"
                    value={selectedUser.state}
                    disabled={dialogType === 'view'}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth disabled={dialogType === 'view'}>
                    <InputLabel>Status</InputLabel>
                    <Select value={selectedUser.status} label="Status">
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                      <MenuItem value="suspended">Suspended</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                {selectedUser.activationKeyInfo && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                        Activation Key Information
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Key ID"
                        value={selectedUser.activationKeyInfo.keyId}
                        disabled
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Short Code"
                        value={selectedUser.activationKeyInfo.shortCode}
                        disabled
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Key Status"
                        value={selectedUser.activationKeyInfo.status}
                        disabled
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Remaining Days"
                        value={selectedUser.activationKeyInfo.remainingDays}
                        disabled
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        {dialogType !== 'add' && (
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancel</Button>
            {dialogType !== 'view' && (
              <Button variant="contained" onClick={handleCloseDialog}>
                Save Changes
              </Button>
            )}
          </DialogActions>
        )}
      </Dialog>
    </Box>
  );
}
