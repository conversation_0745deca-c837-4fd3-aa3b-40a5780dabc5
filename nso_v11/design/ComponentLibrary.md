# NSO Clinical Assistant - Component Library

## 🧩 Reusable UI Components

### Primary Button Component

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Default State
│  │         Button Text         │   │ Background: #2196F3
│  └─────────────────────────────┘   │ Text: #FFFFFF, 16px Medium
│                                     │ Height: 48dp, Radius: 8dp
│  ┌─────────────────────────────┐   │ Pressed State
│  │         Button Text         │   │ Background: #1976D2
│  └─────────────────────────────┘   │ Scale: 0.98, Elevation: 8dp
│                                     │
│  ┌─────────────────────────────┐   │ Disabled State
│  │         Button Text         │   │ Background: #E0E0E0
│  └─────────────────────────────┘   │ Text: #9E9E9E
│                                     │
│  ┌─────────────────────────────┐   │ Loading State
│  │    ⟳    Button Text         │   │ Spinner + Text
│  └─────────────────────────────┘   │ Disabled interaction
└─────────────────────────────────────┘
```

### Secondary Button Component

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Default State
│  │         Button Text         │   │ Background: Transparent
│  └─────────────────────────────┘   │ Border: 2dp #2196F3
│                                     │ Text: #2196F3, 16px Medium
│  ┌─────────────────────────────┐   │ Pressed State
│  │         Button Text         │   │ Background: #E3F2FD
│  └─────────────────────────────┘   │ Border: 2dp #1976D2
│                                     │
│  ┌─────────────────────────────┐   │ Disabled State
│  │         Button Text         │   │ Border: 2dp #E0E0E0
│  └─────────────────────────────┘   │ Text: #9E9E9E
└─────────────────────────────────────┘
```

### Danger Button Component

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Default State
│  │         Delete Item         │   │ Background: #F44336
│  └─────────────────────────────┘   │ Text: #FFFFFF, 16px Medium
│                                     │
│  ┌─────────────────────────────┐   │ Pressed State
│  │         Delete Item         │   │ Background: #D32F2F
│  └─────────────────────────────┘   │ Scale: 0.98
└─────────────────────────────────────┘
```

---

## 📝 Input Components

### Text Input Field

```
┌─────────────────────────────────────┐
│  Label Text                         │ 14px Medium, #757575
│  ┌─────────────────────────────┐   │ Default State
│  │ Placeholder text            │   │ Border: 2dp #E0E0E0
│  └─────────────────────────────┘   │ Height: 56dp, Radius: 8dp
│                                     │
│  Label Text                         │ Focus State
│  ┌─────────────────────────────┐   │ Border: 2dp #2196F3
│  │ User input text|            │   │ Cursor: #2196F3
│  └─────────────────────────────┘   │
│                                     │
│  Label Text                         │ Error State
│  ┌─────────────────────────────┐   │ Border: 2dp #F44336
│  │ Invalid input text          │   │ 
│  └─────────────────────────────┘   │
│  ⚠️ Error message here              │ 12px Regular, #F44336
│                                     │
│  Label Text                         │ Success State
│  ┌─────────────────────────────┐   │ Border: 2dp #4CAF50
│  │ Valid input text         ✓  │   │ Checkmark icon
│  └─────────────────────────────┘   │
└─────────────────────────────────────┘
```

### Dropdown Component

```
┌─────────────────────────────────────┐
│  Role Selection                     │ Label
│  ┌─────────────────────────────┐   │ Closed State
│  │ Select an option         ▼  │   │ Placeholder + Arrow
│  └─────────────────────────────┘   │
│                                     │
│  Role Selection                     │ Open State
│  ┌─────────────────────────────┐   │ Selected option
│  │ Doctor                   ▲  │   │ Arrow up
│  ├─────────────────────────────┤   │
│  │ 👨‍⚕️ Doctor                  │   │ Dropdown options
│  │ 👩‍⚕️ Nurse                   │   │ with icons
│  │ 🏥 Community Health Worker  │   │
│  │ 🩺 Medical Assistant        │   │
│  │ 📋 Other                    │   │
│  └─────────────────────────────┘   │
└─────────────────────────────────────┘
```

### Checkbox Component

```
┌─────────────────────────────────────┐
│  ☐ Unchecked option                 │ Default state
│                                     │ Border: 2dp #757575
│  ☑️ Checked option                  │ Checked state
│                                     │ Background: #2196F3
│  ☐ Disabled option                  │ Disabled state
│                                     │ Border: 2dp #E0E0E0
│                                     │ Text: #9E9E9E
└─────────────────────────────────────┘
```

### Radio Button Component

```
┌─────────────────────────────────────┐
│  ○ Unselected option                │ Default state
│                                     │ Border: 2dp #757575
│  ● Selected option                  │ Selected state
│                                     │ Fill: #2196F3
│  ○ Disabled option                  │ Disabled state
│                                     │ Border: 2dp #E0E0E0
│                                     │ Text: #9E9E9E
└─────────────────────────────────────┘
```

---

## 📋 Card Components

### Standard Card

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Default Card
│  │                             │   │ Background: #FFFFFF
│  │         Card Content        │   │ Border: 1dp #E0E0E0
│  │                             │   │ Radius: 12dp
│  └─────────────────────────────┘   │ Shadow: 0 2dp 8dp rgba(0,0,0,0.1)
└─────────────────────────────────────┘
```

### Elevated Card

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Elevated Card
│  │                             │   │ Background: #FFFFFF
│  │         Card Content        │   │ No border
│  │                             │   │ Radius: 12dp
│  └─────────────────────────────┘   │ Shadow: 0 4dp 16dp rgba(0,0,0,0.15)
└─────────────────────────────────────┘
```

### Status Card

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Success Card
│  │ ✅ Success message          │   │ Background: #E8F5E8
│  │    Additional details       │   │ Border: 1dp #C8E6C9
│  └─────────────────────────────┘   │ Text: #2E7D32
│                                     │
│  ┌─────────────────────────────┐   │ Warning Card
│  │ ⚠️ Warning message          │   │ Background: #FFF3E0
│  │    Additional details       │   │ Border: 1dp #FFE0B2
│  └─────────────────────────────┘   │ Text: #E65100
│                                     │
│  ┌─────────────────────────────┐   │ Error Card
│  │ ❌ Error message            │   │ Background: #FFEBEE
│  │    Additional details       │   │ Border: 1dp #FFCDD2
│  └─────────────────────────────┘   │ Text: #C62828
│                                     │
│  ┌─────────────────────────────┐   │ Info Card
│  │ ℹ️ Information message      │   │ Background: #E3F2FD
│  │    Additional details       │   │ Border: 1dp #BBDEFB
│  └─────────────────────────────┘   │ Text: #1565C0
└─────────────────────────────────────┘
```

---

## 🔄 Loading Components

### Spinner Component

```
┌─────────────────────────────────────┐
│              ⟳                     │ Rotating spinner
│                                     │ Size: 32dp
│         Loading...                  │ Color: #2196F3
│                                     │ Animation: 360° rotation, 2s
└─────────────────────────────────────┘
```

### Progress Bar Component

```
┌─────────────────────────────────────┐
│  Uploading diagnosis...             │ Label text
│  ████████████░░░░░░░░░░░░░░░░░░░░   │ Progress bar
│  75% complete                       │ Percentage text
│                                     │
│  ████████████████████████████████   │ Indeterminate
│  Processing...                      │ Continuous animation
└─────────────────────────────────────┘
```

### Skeleton Loading

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Skeleton Card
│  │ ████████████████░░░░░░░░░░  │   │ Shimmer animation
│  │ ██████░░░░░░░░░░░░░░░░░░░░░░  │   │ Gray placeholders
│  │ ████████████░░░░░░░░░░░░░░░░  │   │ Maintain layout
│  └─────────────────────────────┘   │
└─────────────────────────────────────┘
```

---

## 🔔 Notification Components

### Toast Notification

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Success Toast
│  │ ✅ Diagnosis saved!         │   │ Background: #4CAF50
│  └─────────────────────────────┘   │ Text: #FFFFFF
│                                     │ Auto-dismiss: 3s
│  ┌─────────────────────────────┐   │ Error Toast
│  │ ❌ Upload failed            │   │ Background: #F44336
│  └─────────────────────────────┘   │ Text: #FFFFFF
│                                     │ Manual dismiss
│  ┌─────────────────────────────┐   │ Info Toast
│  │ ℹ️ Syncing in background    │   │ Background: #2196F3
│  └─────────────────────────────┘   │ Text: #FFFFFF
└─────────────────────────────────────┘
```

### Banner Notification

```
┌─────────────────────────────────────┐
│  ┌─────────────────────────────┐   │ Persistent Banner
│  │ 🌐 You're offline           │   │ Full width
│  │ Data will sync when online  │   │ Background: #FF9800
│  │                        ✕   │   │ Dismiss button
│  └─────────────────────────────┘   │
└─────────────────────────────────────┘
```

---

## 🎨 Component Specifications

### Animation Standards
```
Button Press:
- Scale: 1.0 → 0.98 → 1.0
- Duration: 150ms ease-in-out
- Ripple effect: Expand from touch point

Card Hover:
- Elevation: 2dp → 8dp
- Duration: 200ms ease-out
- Scale: 1.0 → 1.02

Loading States:
- Fade in: 300ms ease-out
- Skeleton shimmer: 1.5s infinite
- Spinner rotation: 2s infinite linear
```

### Accessibility Features
```
Touch Targets:
- Minimum size: 48dp x 48dp
- Spacing: 8dp minimum between targets
- Visual feedback: Immediate on touch

Color Contrast:
- Text on background: 4.5:1 minimum
- Interactive elements: 3:1 minimum
- Focus indicators: High contrast borders

Screen Reader Support:
- Semantic labels for all interactive elements
- State announcements for dynamic content
- Navigation landmarks for screen structure
```

### Responsive Behavior
```
Small Screens (320-360dp):
- Reduce padding by 25%
- Stack buttons vertically
- Smaller font sizes for secondary text

Large Screens (480dp+):
- Increase padding by 25%
- Side-by-side button layouts
- Enhanced visual hierarchy

Landscape Mode:
- Horizontal form layouts
- Reduced vertical spacing
- Optimized for thumb reach
```

This component library ensures consistency, accessibility, and professional medical aesthetics across the entire NSO Clinical Assistant application.
