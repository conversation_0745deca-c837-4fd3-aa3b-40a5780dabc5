const crypto = require('crypto');
const config = require('../config');

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits
const SALT_LENGTH = 32; // 256 bits

// Derive encryption key from master secret
function deriveKey(salt) {
  return crypto.pbkdf2Sync(config.ENCRYPTION_SECRET, salt, 100000, KEY_LENGTH, 'sha512');
}

/**
 * Generate a secure activation key with embedded metadata
 * @param {Object} keyData - Key metadata
 * @param {string} keyData.userId - User ID
 * @param {string} keyData.deviceId - Device ID
 * @param {number} keyData.validityMonths - Validity period in months
 * @param {string} keyData.role - User role
 * @param {string} keyData.facility - User facility
 * @returns {string} Encrypted activation key
 */
function generateActivationKey(keyData) {
  try {
    // Create expiry date
    const expiryDate = new Date();
    expiryDate.setMonth(expiryDate.getMonth() + keyData.validityMonths);
    
    // Create payload with metadata
    const payload = {
      userId: keyData.userId,
      deviceId: keyData.deviceId,
      role: keyData.role,
      facility: keyData.facility,
      issuedAt: new Date().toISOString(),
      expiresAt: expiryDate.toISOString(),
      validityMonths: keyData.validityMonths,
      keyVersion: '1.0'
    };
    
    // Convert payload to JSON string
    const payloadString = JSON.stringify(payload);
    const payloadBuffer = Buffer.from(payloadString, 'utf8');
    
    // Generate random salt and IV
    const salt = crypto.randomBytes(SALT_LENGTH);
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Derive encryption key
    const key = deriveKey(salt);
    
    // Create cipher
    const cipher = crypto.createCipherGCM(ALGORITHM, key, iv);
    
    // Encrypt payload
    let encrypted = cipher.update(payloadBuffer);
    cipher.final();
    
    // Get authentication tag
    const tag = cipher.getAuthTag();
    
    // Combine all components: salt + iv + tag + encrypted_data
    const combined = Buffer.concat([salt, iv, tag, encrypted]);
    
    // Encode as base64 and add prefix for identification
    const activationKey = 'NSO_' + combined.toString('base64');
    
    return activationKey;
    
  } catch (error) {
    throw new Error(`Failed to generate activation key: ${error.message}`);
  }
}

/**
 * Decrypt and validate an activation key
 * @param {string} activationKey - Encrypted activation key
 * @returns {Object} Decrypted key data or null if invalid
 */
function decryptActivationKey(activationKey) {
  try {
    // Check prefix
    if (!activationKey.startsWith('NSO_')) {
      throw new Error('Invalid activation key format');
    }
    
    // Remove prefix and decode base64
    const base64Data = activationKey.substring(4);
    const combined = Buffer.from(base64Data, 'base64');
    
    // Extract components
    const salt = combined.slice(0, SALT_LENGTH);
    const iv = combined.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
    const tag = combined.slice(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
    const encrypted = combined.slice(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
    
    // Derive decryption key
    const key = deriveKey(salt);
    
    // Create decipher
    const decipher = crypto.createDecipherGCM(ALGORITHM, key, iv);
    decipher.setAuthTag(tag);
    
    // Decrypt data
    let decrypted = decipher.update(encrypted);
    decipher.final();
    
    // Parse JSON payload
    const payloadString = decrypted.toString('utf8');
    const payload = JSON.parse(payloadString);
    
    // Validate expiry
    const now = new Date();
    const expiryDate = new Date(payload.expiresAt);
    
    if (now > expiryDate) {
      return {
        ...payload,
        isValid: false,
        isExpired: true,
        error: 'Activation key has expired'
      };
    }
    
    return {
      ...payload,
      isValid: true,
      isExpired: false,
      remainingDays: Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24))
    };
    
  } catch (error) {
    return {
      isValid: false,
      isExpired: false,
      error: `Invalid activation key: ${error.message}`
    };
  }
}

/**
 * Validate activation key without decrypting (for quick checks)
 * @param {string} activationKey - Activation key to validate
 * @returns {boolean} True if key format is valid
 */
function isValidKeyFormat(activationKey) {
  try {
    if (!activationKey || typeof activationKey !== 'string') {
      return false;
    }
    
    if (!activationKey.startsWith('NSO_')) {
      return false;
    }
    
    const base64Data = activationKey.substring(4);
    const combined = Buffer.from(base64Data, 'base64');
    
    // Check minimum length (salt + iv + tag + some encrypted data)
    const minLength = SALT_LENGTH + IV_LENGTH + TAG_LENGTH + 16;
    return combined.length >= minLength;
    
  } catch (error) {
    return false;
  }
}

/**
 * Generate a unique device-specific key component
 * @param {string} deviceId - Device identifier
 * @param {string} deviceModel - Device model
 * @param {string} osVersion - OS version
 * @returns {string} Device-specific key component
 */
function generateDeviceKey(deviceId, deviceModel = '', osVersion = '') {
  const deviceString = `${deviceId}:${deviceModel}:${osVersion}`;
  return crypto.createHash('sha256').update(deviceString).digest('hex');
}

/**
 * Create a short activation code for manual entry
 * @param {string} activationKey - Full activation key
 * @returns {string} Short activation code (8-12 characters)
 */
function createShortCode(activationKey) {
  try {
    const hash = crypto.createHash('sha256').update(activationKey).digest('hex');
    // Take first 8 characters and format as XXXX-XXXX
    const shortCode = hash.substring(0, 8).toUpperCase();
    return `${shortCode.substring(0, 4)}-${shortCode.substring(4, 8)}`;
  } catch (error) {
    throw new Error(`Failed to create short code: ${error.message}`);
  }
}

/**
 * Verify a short activation code against a full key
 * @param {string} shortCode - Short activation code
 * @param {string} activationKey - Full activation key
 * @returns {boolean} True if codes match
 */
function verifyShortCode(shortCode, activationKey) {
  try {
    const expectedCode = createShortCode(activationKey);
    return shortCode.toUpperCase().replace('-', '') === expectedCode.replace('-', '');
  } catch (error) {
    return false;
  }
}

module.exports = {
  generateActivationKey,
  decryptActivationKey,
  isValidKeyFormat,
  generateDeviceKey,
  createShortCode,
  verifyShortCode
};
