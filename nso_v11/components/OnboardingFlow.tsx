import React, { useState } from 'react';
import { DeviceInfo, UserProfile, UserStorage } from '../utils/userStorage';
import ActivationScreen from './ActivationScreen';
import CategoryMenuScreen from './CategoryMenuScreen';
import OnboardingScreen from './OnboardingScreen';
import RegistrationScreen from './RegistrationScreen';

interface OnboardingFlowProps {
  onComplete: () => void;
}

interface UserData {
  fullName: string;
  role: string;
  facility: string;
  state: string;
  contactInfo: string;
}

interface CategoryItem {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  ageGroup: string;
  color: string;
  recordCount: number;
}

type OnboardingStep = 'onboarding' | 'activation' | 'registration' | 'menu' | 'complete';

export default function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('onboarding');
  const [activationKey, setActivationKey] = useState<string>('');
  const [userData, setUserData] = useState<UserData | null>(null);

  const handleOnboardingComplete = () => {
    setCurrentStep('activation');
  };

  const handleActivationComplete = async (key: string) => {
    try {
      setActivationKey(key);

      // Save device activation info
      const deviceInfo: DeviceInfo = {
        deviceId: `device_${Date.now()}`, // Generate unique device ID
        activationKey: key,
        isActivated: true,
        activatedAt: new Date().toISOString(),
      };

      await UserStorage.saveDeviceInfo(deviceInfo);
      setCurrentStep('registration');
    } catch (error) {
      console.error('Error saving activation data:', error);
      // Still proceed to registration even if storage fails
      setCurrentStep('registration');
    }
  };

  const handleRegistrationComplete = async (data: UserData) => {
    try {
      setUserData(data);

      // Create and save user profile
      const userProfile: UserProfile = {
        id: `user_${Date.now()}`, // Generate unique user ID
        fullName: data.fullName,
        role: data.role,
        facility: data.facility,
        state: data.state,
        contactInfo: data.contactInfo,
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
      };

      await UserStorage.saveUserProfile(userProfile);
      setCurrentStep('menu');
    } catch (error) {
      console.error('Error saving user profile:', error);
      // Still proceed to menu even if storage fails
      setCurrentStep('menu');
    }
  };

  const handleCategorySelect = (category: CategoryItem) => {
    // Handle category selection - navigate to category details
    console.log('Selected category:', category);
    // For now, complete the onboarding flow
    onComplete();
  };

  switch (currentStep) {
    case 'onboarding':
      return <OnboardingScreen onComplete={handleOnboardingComplete} />;

    case 'activation':
      return <ActivationScreen onActivationComplete={handleActivationComplete} />;

    case 'registration':
      return <RegistrationScreen onRegistrationComplete={handleRegistrationComplete} />;

    case 'menu':
      return <CategoryMenuScreen onCategorySelect={handleCategorySelect} />;

    case 'complete':
    default:
      return null; // This will trigger the onComplete callback
  }
}

export type { CategoryItem, OnboardingStep, UserData };

