# NSO v11 - Onboarding Flow Documentation

## Overview

The NSO v11 app features a comprehensive onboarding flow with a beautiful green theme that guides users through the initial setup process. The flow consists of multiple screens designed to introduce users to the app's features and collect necessary information for account setup.

## Flow Structure

### 1. Splash Screen
- **Duration**: 3 seconds
- **Features**: 
  - Animated logo with pulse effect
  - Green gradient background
  - Loading indicator
  - Smooth transitions

### 2. Onboarding Screens (5 pages)

#### Page 1: Welcome Screen
- **Title**: "Welcome to NSO – Smart Clinical Assistant"
- **Content**: Introduction to the app's purpose
- **Button**: "Get Started"

#### Page 2: Smart Clinical Companion
- **Title**: "Your Smart Clinical Companion"
- **Content**: Explains NSO's core functionality
- **Features Highlighted**:
  - Designed for field and rural conditions
  - Works offline, syncs automatically when online
  - Accurate, context-aware decision support
- **Button**: "Next"

#### Page 3: Low Connectivity Design
- **Title**: "Designed for Low Connectivity"
- **Content**: Offline-first approach explanation
- **Features Highlighted**:
  - Offline first
  - Secure background data sync
  - Fast, responsive UI
- **Button**: "Next"

#### Page 4: Real-time Monitoring
- **Title**: "Supervised and Secure"
- **Content**: Explains tracking and monitoring features
- **Features Highlighted**:
  - Every diagnostic activity recorded
  - GPS tracking included
  - Secure cloud dashboard for administrators
- **Button**: "Next"

#### Page 5: Activation Required
- **Title**: "Secure Access via Activation Key"
- **Content**: Explains activation key requirement
- **Features Highlighted**:
  - One device per activation
  - Keys securely encrypted
  - Validity duration built-in
- **Button**: "Proceed to Activation"

### 3. Activation Screen
- **Title**: "Enter Your Activation Key"
- **Features**:
  - Formatted input field (XXXX-XXXX-XXXX-XXXX)
  - Auto-formatting and validation
  - Security information display
  - Loading state during activation
- **Button**: "Activate"

### 4. Registration Screen
- **Title**: "Create Your Profile"
- **Form Fields**:
  - Full Name (required)
  - Role (dropdown with predefined options)
  - Facility (required)
  - State (required)
  - Contact Information (required)
- **Features**:
  - Form validation
  - Dropdown for role selection
  - Privacy notice
- **Button**: "Complete Registration"

## Design Features

### Color Scheme
- **Primary Colors**: Green gradient (#1B5E20 → #2E7D32 → #388E3C → #43A047)
- **Text**: White with various opacity levels
- **Accents**: Semi-transparent white overlays

### Animations
- **Fade In/Out**: Smooth opacity transitions
- **Slide Transitions**: Vertical slide animations between screens
- **Pulse Effects**: Logo and loading indicators
- **Progress Indicators**: Animated progress bar and page dots

### UI Components
- **Progress Bar**: Shows completion percentage
- **Page Indicators**: Dots showing current page
- **Feature Lists**: Bullet points with highlights
- **Cards**: Semi-transparent containers with shadows
- **Buttons**: Prominent white buttons with shadows

## Technical Implementation

### Components Structure
```
components/
├── SplashScreen.tsx          # Initial loading screen
├── OnboardingScreen.tsx      # Multi-page onboarding
├── ActivationScreen.tsx      # Activation key input
├── RegistrationScreen.tsx    # User profile creation
└── OnboardingFlow.tsx        # Main flow orchestrator
```

### State Management
- Uses React hooks for local state management
- Tracks current onboarding step
- Manages form data and validation
- Handles loading states

### Navigation Flow
1. App Launch → Splash Screen
2. Splash Complete → Onboarding Screens
3. Onboarding Complete → Activation Screen
4. Activation Complete → Registration Screen
5. Registration Complete → Main App

### Data Persistence
- Activation key and user data are passed through the flow
- In production, data should be saved to AsyncStorage
- Onboarding completion status should be persisted

## Customization

### Theme Constants
The app uses a centralized theme system located in `constants/theme.ts`:
- Colors
- Typography
- Spacing
- Border Radius
- Shadows

### Content Updates
To update onboarding content, modify the `onboardingData` array in `OnboardingScreen.tsx`.

### Role Options
To modify available roles, update the `roles` array in `RegistrationScreen.tsx`.

## Testing

### Development Mode
- The app always shows onboarding in development mode
- Use the "Reset Onboarding" debug button on the main screen
- Reload the app to see the onboarding flow again

### Production Considerations
- Implement AsyncStorage to track onboarding completion
- Add proper error handling for network requests
- Implement actual activation key validation
- Add analytics tracking for onboarding completion rates

## Accessibility

### Features Implemented
- Proper color contrast ratios
- Readable font sizes
- Touch target sizes meet minimum requirements
- Keyboard navigation support for forms

### Future Improvements
- Add screen reader support
- Implement voice-over descriptions
- Add high contrast mode option
- Support for larger text sizes

## Performance

### Optimizations
- Lazy loading of screens
- Optimized animations using native driver
- Minimal re-renders through proper state management
- Efficient image and asset loading

### Bundle Size
- Uses only necessary dependencies
- Tree-shaking for unused code
- Optimized asset sizes

## Security

### Data Protection
- Form validation on client side
- Secure storage recommendations for sensitive data
- No sensitive data logged to console in production

### Activation Key Security
- Client-side formatting and basic validation
- Server-side validation should be implemented
- Encrypted storage for activation keys
