const logger = require('../utils/logger');
const config = require('../config');

/**
 * Global error handler middleware
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  logger.error('Error Handler:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId,
    deviceId: req.user?.deviceId
  });

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Invalid resource ID';
    error = {
      message,
      code: 'INVALID_ID',
      statusCode: 400
    };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    const message = `Duplicate value for field: ${field}`;
    error = {
      message,
      code: 'DUPLICATE_FIELD',
      statusCode: 400,
      field
    };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors).map(val => ({
      field: val.path,
      message: val.message,
      value: val.value
    }));
    
    error = {
      message: 'Validation failed',
      code: 'VALIDATION_ERROR',
      statusCode: 400,
      details: errors
    };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = {
      message: 'Invalid token',
      code: 'INVALID_TOKEN',
      statusCode: 401
    };
  }

  if (err.name === 'TokenExpiredError') {
    error = {
      message: 'Token expired',
      code: 'TOKEN_EXPIRED',
      statusCode: 401
    };
  }

  // MongoDB connection errors
  if (err.name === 'MongoNetworkError' || err.name === 'MongoTimeoutError') {
    error = {
      message: 'Database connection error',
      code: 'DATABASE_ERROR',
      statusCode: 503
    };
  }

  // Rate limiting errors
  if (err.name === 'TooManyRequestsError') {
    error = {
      message: 'Too many requests',
      code: 'RATE_LIMIT_EXCEEDED',
      statusCode: 429,
      retryAfter: err.retryAfter
    };
  }

  // File upload errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    error = {
      message: 'File too large',
      code: 'FILE_TOO_LARGE',
      statusCode: 413,
      maxSize: config.MAX_FILE_SIZE
    };
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    error = {
      message: 'Too many files',
      code: 'TOO_MANY_FILES',
      statusCode: 413
    };
  }

  // Sync-specific errors
  if (err.code === 'SYNC_CONFLICT') {
    error = {
      message: 'Data synchronization conflict',
      code: 'SYNC_CONFLICT',
      statusCode: 409,
      conflicts: err.conflicts
    };
  }

  if (err.code === 'SYNC_TIMEOUT') {
    error = {
      message: 'Synchronization timeout',
      code: 'SYNC_TIMEOUT',
      statusCode: 408
    };
  }

  // Device-specific errors
  if (err.code === 'DEVICE_NOT_ACTIVATED') {
    error = {
      message: 'Device not activated',
      code: 'DEVICE_NOT_ACTIVATED',
      statusCode: 403
    };
  }

  if (err.code === 'DEVICE_LIMIT_EXCEEDED') {
    error = {
      message: 'Device limit exceeded for user',
      code: 'DEVICE_LIMIT_EXCEEDED',
      statusCode: 403
    };
  }

  // Default to 500 server error
  const statusCode = error.statusCode || 500;
  const code = error.code || 'INTERNAL_ERROR';
  
  // Prepare error response
  const errorResponse = {
    success: false,
    error: error.message || 'Internal server error',
    code,
    timestamp: new Date().toISOString()
  };

  // Add additional error details in development
  if (config.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
    errorResponse.details = error.details;
  }

  // Add specific error fields
  if (error.field) errorResponse.field = error.field;
  if (error.details) errorResponse.details = error.details;
  if (error.conflicts) errorResponse.conflicts = error.conflicts;
  if (error.retryAfter) errorResponse.retryAfter = error.retryAfter;
  if (error.maxSize) errorResponse.maxSize = error.maxSize;

  res.status(statusCode).json(errorResponse);
};

/**
 * Handle async errors
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * Create custom error
 */
class CustomError extends Error {
  constructor(message, statusCode = 500, code = 'CUSTOM_ERROR', details = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.name = 'CustomError';
  }
}

/**
 * Create sync conflict error
 */
class SyncConflictError extends CustomError {
  constructor(message, conflicts = []) {
    super(message, 409, 'SYNC_CONFLICT');
    this.conflicts = conflicts;
    this.name = 'SyncConflictError';
  }
}

/**
 * Create validation error
 */
class ValidationError extends CustomError {
  constructor(message, details = []) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

/**
 * Create authentication error
 */
class AuthenticationError extends CustomError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

/**
 * Create authorization error
 */
class AuthorizationError extends CustomError {
  constructor(message = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

/**
 * Create not found error
 */
class NotFoundError extends CustomError {
  constructor(message = 'Resource not found') {
    super(message, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

/**
 * Create rate limit error
 */
class RateLimitError extends CustomError {
  constructor(message = 'Rate limit exceeded', retryAfter = 60) {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
    this.retryAfter = retryAfter;
    this.name = 'RateLimitError';
  }
}

/**
 * Handle 404 errors
 */
const notFound = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

module.exports = {
  errorHandler,
  asyncHandler,
  notFound,
  CustomError,
  SyncConflictError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  RateLimitError
};
