# NSO Clinical Assistant - Onboarding Screens Design

## 🚀 Onboarding Flow (3 Screens)

### Screen 1: Welcome Screen

```
┌─────────────────────────────────────┐
│ ●●●                            ●●● │ Status Bar
├─────────────────────────────────────┤
│                                     │
│              [NSO LOGO]             │ 64dp logo
│                                     │
│         Welcome to NSO              │ H1, Center
│      Clinical Assistant             │ H1, Center
│                                     │
│    [ILLUSTRATION: Doctor with       │ 200dp height
│     stethoscope examining patient   │ Medical blue tones
│     in field setting]              │
│                                     │
│   Diagnose and record clinical      │ Body Large, Center
│   activities securely —            │ #757575
│   anywhere, anytime.               │
│                                     │
│                                     │
│  ○ ● ○                             │ Page indicators
│                                     │
│  ┌─────────────────────────────┐   │
│  │           Next              │   │ Primary Button
│  └─────────────────────────────┘   │ Full width - 32dp margin
│                                     │
│           Skip Intro                │ Text button, center
│                                     │
└─────────────────────────────────────┘
```

**Visual Elements:**
- **Background:** Clean white with subtle medical pattern
- **Logo:** NSO emblem with medical cross
- **Illustration:** Flat design, medical blue (#2196F3) and green (#4CAF50)
- **Typography:** Roboto, professional hierarchy
- **Animation:** Gentle fade-in with slide-up effect

---

### Screen 2: Features Screen

```
┌─────────────────────────────────────┐
│ ●●●                            ●●● │ Status Bar
├─────────────────────────────────────┤
│                                     │
│           What You Can Do           │ H1, Center, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │  🩺  Quick symptom-based    │   │ Feature Card 1
│  │      diagnosis              │   │ Icon + text
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │  📱  Works offline, syncs   │   │ Feature Card 2
│  │      when online            │   │ 16dp spacing
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │  📋  View diagnosis         │   │ Feature Card 3
│  │      history                │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │  👨‍⚕️  Admin gets real-time  │   │ Feature Card 4
│  │      updates                │   │
│  └─────────────────────────────┘   │
│                                     │
│  ○ ● ○                             │ Page indicators
│                                     │
│  ┌─────────────────────────────┐   │
│  │           Next              │   │ Primary Button
│  └─────────────────────────────┘   │
│                                     │
│           Back                      │ Text button, center
│                                     │
└─────────────────────────────────────┘
```

**Feature Cards Design:**
- **Background:** White cards with subtle shadow
- **Border Radius:** 12dp
- **Padding:** 20dp
- **Icons:** 32dp, medical blue (#2196F3)
- **Text:** 16px medium weight
- **Spacing:** 16dp between cards
- **Animation:** Staggered slide-in from right

---

### Screen 3: Secure Access Screen

```
┌─────────────────────────────────────┐
│ ●●●                            ●●● │ Status Bar
├─────────────────────────────────────┤
│                                     │
│          Secure Activation          │ H1, Center
│                                     │
│    [ILLUSTRATION: Shield with       │ 160dp height
│     medical cross, lock icon,       │ Security themed
│     and key symbols]               │ Blue and green
│                                     │
│                                     │
│   Activate your device using        │ Body Large, Center
│   your admin-provided key.          │ #757575, line height 1.5
│                                     │
│   This ensures secure access        │ Body Medium, Center
│   and compliance with medical       │ #9E9E9E
│   data protection standards.        │
│                                     │
│                                     │
│  ┌─────────────────────────────┐   │
│  │  🔐  One-time activation    │   │ Security Feature 1
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │  📱  Device-locked access   │   │ Security Feature 2
│  └─────────────────────────────┘   │
│                                     │
│  ○ ○ ●                             │ Page indicators
│                                     │
│  ┌─────────────────────────────┐   │
│  │        Get Started          │   │ Primary Button
│  └─────────────────────────────┘   │
│                                     │
│           Back                      │ Text button, center
│                                     │
└─────────────────────────────────────┘
```

**Security Visual Elements:**
- **Shield Icon:** Gradient from blue to green
- **Lock Animation:** Subtle rotation on load
- **Security Badges:** Small cards with lock icons
- **Color Scheme:** Trust-building blues and greens
- **Typography:** Clear, professional, confidence-inspiring

---

## 🎨 Onboarding Design Specifications

### Animation Sequence
1. **Screen Entry:** 400ms slide from right
2. **Content Load:** Staggered fade-in (100ms delays)
3. **Illustration:** Gentle scale-in animation
4. **Button Hover:** 150ms scale and shadow increase
5. **Page Transition:** 300ms slide with overlap

### Accessibility Features
- **High Contrast:** WCAG AA compliant color ratios
- **Large Touch Targets:** Minimum 48dp for all interactive elements
- **Screen Reader Support:** Proper semantic markup
- **Font Scaling:** Supports system font size preferences
- **Focus Indicators:** Clear visual focus states

### Responsive Behavior
- **Small Screens (320dp):** Reduced illustration size, tighter spacing
- **Large Screens (480dp+):** Centered content with max-width constraints
- **Landscape Mode:** Horizontal layout with side-by-side content
- **Tablet Adaptation:** Larger illustrations and increased spacing

### Micro-interactions
- **Page Indicators:** Smooth transition between states
- **Button Press:** Ripple effect with primary color
- **Card Hover:** Subtle elevation increase
- **Skip Animation:** Fast-forward through remaining screens
- **Progress Feedback:** Visual indication of onboarding progress

This onboarding design creates a professional, trustworthy first impression while clearly communicating the app's medical purpose and security features.
