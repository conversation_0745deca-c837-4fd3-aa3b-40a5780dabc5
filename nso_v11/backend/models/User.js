const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // Basic Information
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  fullName: {
    type: String,
    required: true,
    trim: true
  },
  role: {
    type: String,
    required: true,
    enum: ['Doctor', 'Nurse', 'Medical Officer', 'Healthcare Worker', 'Administrator']
  },
  facility: {
    type: String,
    required: true,
    trim: true
  },
  state: {
    type: String,
    required: true,
    trim: true
  },
  contactInfo: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    index: true
  },
  
  // Device Information
  deviceInfo: {
    deviceId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    activationKeyId: {
      type: String,
      required: true,
      index: true
    },
    isActivated: {
      type: Boolean,
      default: false
    },
    activatedAt: {
      type: Date
    },
    deviceModel: String,
    osVersion: String,
    appVersion: String,
    deviceFingerprint: String
  },
  
  // Authentication
  passwordHash: {
    type: String,
    select: false // Don't include in queries by default
  },
  
  // Profile Settings
  profilePicture: String,
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'auto'
    },
    language: {
      type: String,
      default: 'en'
    },
    notifications: {
      enabled: {
        type: Boolean,
        default: true
      },
      syncReminders: {
        type: Boolean,
        default: true
      },
      criticalAlerts: {
        type: Boolean,
        default: true
      }
    },
    offline: {
      autoSync: {
        type: Boolean,
        default: true
      },
      syncInterval: {
        type: Number,
        default: 30 // minutes
      }
    }
  },
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  lastLoginAt: {
    type: Date
  },
  lastSyncAt: {
    type: Date
  },
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.passwordHash;
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
userSchema.index({ userId: 1, 'deviceInfo.deviceId': 1 });
userSchema.index({ facility: 1, state: 1 });
userSchema.index({ role: 1 });
userSchema.index({ lastLoginAt: -1 });
userSchema.index({ lastSyncAt: -1 });

// Pre-save middleware
userSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Methods
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.passwordHash) return false;
  return bcrypt.compare(candidatePassword, this.passwordHash);
};

userSchema.methods.setPassword = async function(password) {
  this.passwordHash = await bcrypt.hash(password, 12);
};

userSchema.methods.updateLastLogin = function() {
  this.lastLoginAt = new Date();
  return this.save();
};

userSchema.methods.updateLastSync = function() {
  this.lastSyncAt = new Date();
  return this.save();
};

// Static methods
userSchema.statics.findByDeviceId = function(deviceId) {
  return this.findOne({ 'deviceInfo.deviceId': deviceId });
};

userSchema.statics.findByUserId = function(userId) {
  return this.findOne({ userId });
};

userSchema.statics.getActiveUsers = function() {
  return this.find({ isActive: true });
};

userSchema.statics.getUsersByFacility = function(facility) {
  return this.find({ facility, isActive: true });
};

userSchema.statics.getUsersByRole = function(role) {
  return this.find({ role, isActive: true });
};

module.exports = mongoose.model('User', userSchema);
