const express = require('express');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

const User = require('../models/User');
const ActivationKey = require('../models/ActivationKey');
const UserActivity = require('../models/UserActivity');
const { generateActivationKey, createShortCode } = require('../utils/encryption');
const logger = require('../utils/logger');

// Admin authentication middleware (simplified for demo)
const adminAuth = (req, res, next) => {
  // In production, implement proper admin authentication
  const adminToken = req.headers['x-admin-token'];
  if (!adminToken || adminToken !== 'admin-secret-token') {
    return res.status(401).json({
      success: false,
      error: 'Admin authentication required',
      code: 'ADMIN_AUTH_REQUIRED'
    });
  }
  
  // Mock admin user
  req.admin = {
    adminId: 'admin_001',
    adminName: 'System Administrator',
    adminEmail: '<EMAIL>'
  };
  
  next();
};

// GET /api/v1/admin/users - Get all users with pagination and filters
router.get('/users', adminAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = 'all',
      role = 'all',
      facility = 'all',
      state = 'all'
    } = req.query;

    // Build filter query
    const filter = {};
    
    if (search) {
      filter.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { userId: { $regex: search, $options: 'i' } },
        { 'deviceInfo.deviceId': { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status !== 'all') {
      if (status === 'active') {
        filter.isActive = true;
        filter['deviceInfo.isActivated'] = true;
      } else if (status === 'inactive') {
        filter.$or = [
          { isActive: false },
          { 'deviceInfo.isActivated': false }
        ];
      }
    }
    
    if (role !== 'all') {
      filter.role = role;
    }
    
    if (facility !== 'all') {
      filter.facility = facility;
    }
    
    if (state !== 'all') {
      filter.state = state;
    }

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const users = await User.find(filter)
      .select('-passwordHash')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(filter);

    // Get activation key info for each user
    const usersWithKeyInfo = await Promise.all(users.map(async (user) => {
      const activationKey = await ActivationKey.findOne({ 
        userId: user.userId,
        keyId: user.deviceInfo.activationKeyId 
      });
      
      return {
        ...user.toJSON(),
        activationKeyInfo: activationKey ? {
          keyId: activationKey.keyId,
          shortCode: activationKey.shortCode,
          status: activationKey.status,
          expiresAt: activationKey.expiresAt,
          remainingDays: activationKey.getRemainingDays(),
          createdBy: activationKey.createdBy
        } : null
      };
    }));

    res.status(200).json({
      success: true,
      data: {
        users: usersWithKeyInfo,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin get users error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// POST /api/v1/admin/users - Create new user with activation key
router.post('/users', adminAuth, async (req, res) => {
  try {
    const {
      fullName,
      email,
      role,
      facility,
      state,
      contactInfo,
      deviceId,
      validityMonths = 12,
      notes = ''
    } = req.body;

    // Validate required fields
    if (!fullName || !email || !role || !facility || !state || !contactInfo || !deviceId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        code: 'MISSING_FIELDS'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email },
        { 'deviceInfo.deviceId': deviceId }
      ]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email or device ID already exists',
        code: 'USER_EXISTS'
      });
    }

    // Generate unique user ID and key ID
    const userId = `NSO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const keyId = `KEY_${Date.now()}_${uuidv4().substr(0, 8)}`;

    // Generate activation key
    const activationKeyData = {
      userId,
      deviceId,
      role,
      facility,
      validityMonths: parseInt(validityMonths)
    };

    const encryptedKey = generateActivationKey(activationKeyData);
    const shortCode = createShortCode(encryptedKey);

    // Create activation key record
    const activationKey = new ActivationKey({
      keyId,
      activationKey: encryptedKey,
      shortCode,
      userId,
      deviceId,
      role,
      facility,
      state,
      validityMonths: parseInt(validityMonths),
      expiresAt: new Date(Date.now() + parseInt(validityMonths) * 30 * 24 * 60 * 60 * 1000),
      createdBy: req.admin,
      notes
    });

    await activationKey.save();

    // Create user record
    const user = new User({
      userId,
      fullName,
      email,
      role,
      facility,
      state,
      contactInfo,
      deviceInfo: {
        deviceId,
        activationKeyId: keyId,
        isActivated: false
      },
      isActive: true
    });

    await user.save();

    // Log admin activity
    logger.info('Admin created user', {
      adminId: req.admin.adminId,
      userId,
      email,
      facility
    });

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        user: {
          userId: user.userId,
          fullName: user.fullName,
          email: user.email,
          role: user.role,
          facility: user.facility,
          state: user.state,
          deviceId: user.deviceInfo.deviceId
        },
        activationKey: {
          keyId: activationKey.keyId,
          activationKey: encryptedKey,
          shortCode: shortCode,
          expiresAt: activationKey.expiresAt,
          validityMonths: activationKey.validityMonths
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/admin/users/:userId - Get specific user details
router.get('/users/:userId', adminAuth, async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findOne({ userId }).select('-passwordHash');
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Get activation keys for this user
    const activationKeys = await ActivationKey.find({ userId }).sort({ createdAt: -1 });

    // Get recent activity
    const recentActivity = await UserActivity.find({ userId })
      .sort({ timestamp: -1 })
      .limit(10);

    res.status(200).json({
      success: true,
      data: {
        user: user.toJSON(),
        activationKeys,
        recentActivity
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin get user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// PUT /api/v1/admin/users/:userId - Update user
router.put('/users/:userId', adminAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;

    // Remove fields that shouldn't be updated
    delete updateData.userId;
    delete updateData.deviceInfo;
    delete updateData.passwordHash;

    const user = await User.findOneAndUpdate(
      { userId },
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select('-passwordHash');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    logger.info('Admin updated user', {
      adminId: req.admin.adminId,
      userId,
      updatedFields: Object.keys(updateData)
    });

    res.status(200).json({
      success: true,
      message: 'User updated successfully',
      data: { user },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// DELETE /api/v1/admin/users/:userId - Deactivate user
router.delete('/users/:userId', adminAuth, async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findOneAndUpdate(
      { userId },
      { isActive: false, updatedAt: new Date() },
      { new: true }
    ).select('-passwordHash');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Revoke all active activation keys
    await ActivationKey.updateMany(
      { userId, status: 'active' },
      { status: 'revoked', adminNotes: `User deactivated by admin ${req.admin.adminName}` }
    );

    logger.info('Admin deactivated user', {
      adminId: req.admin.adminId,
      userId
    });

    res.status(200).json({
      success: true,
      message: 'User deactivated successfully',
      data: { user },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin deactivate user error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/admin/activation-keys - Get all activation keys
router.get('/activation-keys', adminAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status = 'all',
      search = ''
    } = req.query;

    const filter = {};

    if (status !== 'all') {
      filter.status = status;
    }

    if (search) {
      filter.$or = [
        { keyId: { $regex: search, $options: 'i' } },
        { shortCode: { $regex: search, $options: 'i' } },
        { userId: { $regex: search, $options: 'i' } },
        { facility: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const keys = await ActivationKey.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await ActivationKey.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: {
        keys,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin get activation keys error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// POST /api/v1/admin/activation-keys/:keyId/revoke - Revoke activation key
router.post('/activation-keys/:keyId/revoke', adminAuth, async (req, res) => {
  try {
    const { keyId } = req.params;
    const { reason = 'Revoked by admin' } = req.body;

    const activationKey = await ActivationKey.findOne({ keyId });
    if (!activationKey) {
      return res.status(404).json({
        success: false,
        error: 'Activation key not found',
        code: 'KEY_NOT_FOUND'
      });
    }

    await activationKey.revoke(`${reason} (Admin: ${req.admin.adminName})`);

    logger.info('Admin revoked activation key', {
      adminId: req.admin.adminId,
      keyId,
      reason
    });

    res.status(200).json({
      success: true,
      message: 'Activation key revoked successfully',
      data: { activationKey },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin revoke key error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/admin/dashboard/stats - Get dashboard statistics
router.get('/dashboard/stats', adminAuth, async (req, res) => {
  try {
    // Get user statistics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true, 'deviceInfo.isActivated': true });
    const inactiveUsers = await User.countDocuments({
      $or: [
        { isActive: false },
        { 'deviceInfo.isActivated': false }
      ]
    });

    // Get activation key statistics
    const totalKeys = await ActivationKey.countDocuments();
    const activeKeys = await ActivationKey.countDocuments({ status: 'active' });
    const usedKeys = await ActivationKey.countDocuments({ status: 'used' });
    const expiredKeys = await ActivationKey.countDocuments({ status: 'expired' });
    const revokedKeys = await ActivationKey.countDocuments({ status: 'revoked' });

    // Get recent activity
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('userId fullName role facility createdAt');

    const recentKeys = await ActivationKey.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('keyId shortCode status facility createdAt createdBy');

    res.status(200).json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          active: activeUsers,
          inactive: inactiveUsers
        },
        activationKeys: {
          total: totalKeys,
          active: activeKeys,
          used: usedKeys,
          expired: expiredKeys,
          revoked: revokedKeys
        },
        recent: {
          users: recentUsers,
          keys: recentKeys
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Admin dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

module.exports = router;
