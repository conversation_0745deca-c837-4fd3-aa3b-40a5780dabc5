const express = require('express');
const router = express.Router();

const UserActivity = require('../models/UserActivity');
const SyncData = require('../models/SyncData');
const User = require('../models/User');
const auth = require('../middleware/auth');
const logger = require('../utils/logger');

// GET /api/v1/analytics/dashboard - Get dashboard analytics
router.get('/dashboard', auth.auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { days = 7 } = req.query;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    // Get activity stats
    const activityStats = await UserActivity.getUserStats(userId, parseInt(days));
    
    // Get sync stats
    const syncStats = await SyncData.getSyncStats(userId, parseInt(days));
    
    // Get feature usage
    const featureUsage = await UserActivity.getFeatureUsage(parseInt(days));
    
    // Get performance metrics
    const performanceMetrics = await UserActivity.getPerformanceMetrics(userId, parseInt(days));
    
    res.status(200).json({
      success: true,
      analytics: {
        period: `${days} days`,
        activity: activityStats,
        sync: syncStats,
        features: featureUsage,
        performance: performanceMetrics
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/analytics/usage - Get usage analytics
router.get('/usage', auth.auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { days = 30, groupBy = 'day' } = req.query;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    let groupFormat;
    switch (groupBy) {
      case 'hour':
        groupFormat = '%Y-%m-%d %H:00:00';
        break;
      case 'day':
        groupFormat = '%Y-%m-%d';
        break;
      case 'week':
        groupFormat = '%Y-%U';
        break;
      case 'month':
        groupFormat = '%Y-%m';
        break;
      default:
        groupFormat = '%Y-%m-%d';
    }
    
    const usageData = await UserActivity.aggregate([
      {
        $match: {
          userId,
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            period: { $dateToString: { format: groupFormat, date: '$timestamp' } },
            activityType: '$activityType'
          },
          count: { $sum: 1 },
          avgDuration: { $avg: '$duration' }
        }
      },
      {
        $sort: { '_id.period': 1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      usage: usageData,
      period: `${days} days`,
      groupBy,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Usage analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

// GET /api/v1/analytics/errors - Get error analytics
router.get('/errors', auth.auth, async (req, res) => {
  try {
    const { userId } = req.user;
    const { days = 7, severity } = req.query;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));
    
    const query = {
      userId,
      activityType: 'error_occurred',
      timestamp: { $gte: startDate }
    };
    
    if (severity) {
      query['error.severity'] = severity;
    }
    
    const errorData = await UserActivity.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            code: '$error.code',
            severity: '$error.severity'
          },
          count: { $sum: 1 },
          lastOccurrence: { $max: '$timestamp' },
          screens: { $addToSet: '$screen.name' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);
    
    res.status(200).json({
      success: true,
      errors: errorData,
      period: `${days} days`,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Error analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

module.exports = router;
