import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  LocationOn as LocationIcon,
  Smartphone as SmartphoneIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  ComposedChart,
} from 'recharts';

// Mock data
const userGrowthData = [
  { month: 'Jan', users: 1200, newUsers: 150, activeUsers: 980 },
  { month: 'Feb', users: 1450, newUsers: 250, activeUsers: 1180 },
  { month: 'Mar', users: 1680, newUsers: 230, activeUsers: 1350 },
  { month: 'Apr', users: 1920, newUsers: 240, activeUsers: 1580 },
  { month: 'May', users: 2180, newUsers: 260, activeUsers: 1820 },
  { month: 'Jun', users: 2450, newUsers: 270, activeUsers: 2050 },
];

const geographicData = [
  { state: 'Lagos', users: 3420, percentage: 28.5, color: '#3b82f6' },
  { state: 'Abuja', users: 2180, percentage: 18.2, color: '#10b981' },
  { state: 'Kano', users: 1890, percentage: 15.8, color: '#f59e0b' },
  { state: 'Rivers', users: 1560, percentage: 13.0, color: '#ef4444' },
  { state: 'Oyo', users: 1240, percentage: 10.3, color: '#8b5cf6' },
  { state: 'Others', users: 1710, percentage: 14.2, color: '#6b7280' },
];

const deviceData = [
  { name: 'Android', value: 68, users: 8156, color: '#3b82f6' },
  { name: 'iOS', value: 32, users: 3844, color: '#10b981' },
];

const facilityTypeData = [
  { type: 'General Hospital', count: 45, users: 4200 },
  { type: 'Primary Health Center', count: 128, users: 3800 },
  { type: 'Specialist Hospital', count: 23, users: 2100 },
  { type: 'Private Clinic', count: 67, users: 1900 },
  { type: 'Teaching Hospital', count: 12, users: 1000 },
];

const activityData = [
  { hour: '00', activities: 120 },
  { hour: '04', activities: 80 },
  { hour: '08', activities: 450 },
  { hour: '12', activities: 680 },
  { hour: '16', activities: 520 },
  { hour: '20', activities: 340 },
];

const topFeatures = [
  { feature: 'Diagnosis Creation', usage: 85, trend: 'up' },
  { feature: 'Clinical Records', usage: 78, trend: 'up' },
  { feature: 'Data Sync', usage: 72, trend: 'down' },
  { feature: 'Patient Search', usage: 68, trend: 'up' },
  { feature: 'Report Generation', usage: 45, trend: 'up' },
];

export default function Analytics() {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('users');

  return (
    <Box>
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
              Analytics Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Comprehensive insights into app usage and user behavior
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 2 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={(e) => setTimeRange(e.target.value)}
              >
                <MenuItem value="7d">Last 7 days</MenuItem>
                <MenuItem value="30d">Last 30 days</MenuItem>
                <MenuItem value="90d">Last 90 days</MenuItem>
                <MenuItem value="1y">Last year</MenuItem>
              </Select>
            </FormControl>
            
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Metric</InputLabel>
              <Select
                value={selectedMetric}
                label="Metric"
                onChange={(e) => setSelectedMetric(e.target.value)}
              >
                <MenuItem value="users">Users</MenuItem>
                <MenuItem value="sessions">Sessions</MenuItem>
                <MenuItem value="activities">Activities</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>
      </motion.div>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { title: 'Total Users', value: '12,847', change: '+12.5%', trend: 'up', icon: <PeopleIcon /> },
          { title: 'Monthly Active', value: '8,234', change: '****%', trend: 'up', icon: <TrendingUpIcon /> },
          { title: 'Avg Session Time', value: '24m 32s', change: '****%', trend: 'up', icon: <AssessmentIcon /> },
          { title: 'Feature Adoption', value: '78.5%', change: '-2.3%', trend: 'down', icon: <SmartphoneIcon /> },
        ].map((metric, index) => (
          <Grid item xs={12} sm={6} md={3} key={metric.title}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: metric.trend === 'up' ? 'success.light' : 'error.light',
                        color: metric.trend === 'up' ? 'success.dark' : 'error.dark',
                        mr: 2,
                      }}
                    >
                      {metric.icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 700 }}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {metric.title}
                      </Typography>
                    </Box>
                  </Box>
                  <Chip
                    label={metric.change}
                    size="small"
                    color={metric.trend === 'up' ? 'success' : 'error'}
                    icon={metric.trend === 'up' ? <TrendingUpIcon /> : <TrendingDownIcon />}
                  />
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* User Growth Chart */}
        <Grid item xs={12} lg={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card sx={{ height: 400 }}>
              <CardContent sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  User Growth Trends
                </Typography>
                
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={userGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Area
                      type="monotone"
                      dataKey="users"
                      fill="url(#colorUsers)"
                      stroke="#3b82f6"
                      fillOpacity={0.3}
                    />
                    <Bar dataKey="newUsers" fill="#10b981" />
                    <Line
                      type="monotone"
                      dataKey="activeUsers"
                      stroke="#f59e0b"
                      strokeWidth={3}
                    />
                    <defs>
                      <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Geographic Distribution */}
        <Grid item xs={12} lg={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card sx={{ height: 400 }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  Geographic Distribution
                </Typography>
                
                {geographicData.map((item, index) => (
                  <Box key={item.state} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {item.state}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {item.users.toLocaleString()} ({item.percentage}%)
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={item.percentage}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        bgcolor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          bgcolor: item.color,
                          borderRadius: 4,
                        },
                      }}
                    />
                  </Box>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Bottom Section */}
      <Grid container spacing={3}>
        {/* Device Distribution */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  Device Distribution
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <ResponsiveContainer width="50%" height={150}>
                    <PieChart>
                      <Pie
                        data={deviceData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={60}
                        dataKey="value"
                      >
                        {deviceData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                  
                  <Box sx={{ flex: 1 }}>
                    {deviceData.map((item) => (
                      <Box key={item.name} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            bgcolor: item.color,
                            mr: 2,
                          }}
                        />
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {item.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {item.users.toLocaleString()} users ({item.value}%)
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        {/* Top Features */}
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Card>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  Feature Usage
                </Typography>
                
                {topFeatures.map((feature, index) => (
                  <Box key={feature.feature} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {feature.feature}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                          {feature.usage}%
                        </Typography>
                        {feature.trend === 'up' ? (
                          <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main' }} />
                        ) : (
                          <TrendingDownIcon sx={{ fontSize: 16, color: 'error.main' }} />
                        )}
                      </Box>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={feature.usage}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        bgcolor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          bgcolor: feature.trend === 'up' ? 'success.main' : 'warning.main',
                          borderRadius: 3,
                        },
                      }}
                    />
                  </Box>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
}
