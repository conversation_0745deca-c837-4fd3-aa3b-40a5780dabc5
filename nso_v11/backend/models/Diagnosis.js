const mongoose = require('mongoose');

const diagnosisSchema = new mongoose.Schema({
  // Diagnosis Information
  diagnosisId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Patient Information
  patientId: {
    type: String,
    required: true,
    index: true
  },
  patientName: {
    type: String,
    required: true
  },
  patientAge: {
    type: Number,
    required: true
  },
  patientGender: {
    type: String,
    enum: ['male', 'female', 'other'],
    required: true
  },
  
  // Primary Diagnosis
  primaryDiagnosis: {
    condition: {
      type: String,
      required: true
    },
    icdCode: {
      type: String
    },
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe', 'critical'],
      required: true
    },
    confidence: {
      type: String,
      enum: ['confirmed', 'probable', 'suspected'],
      default: 'confirmed'
    }
  },
  
  // Secondary Diagnoses
  secondaryDiagnoses: [{
    condition: String,
    icdCode: String,
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe', 'critical']
    },
    notes: String
  }],
  
  // Symptoms
  presentingSymptoms: [{
    symptom: String,
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe']
    },
    duration: String,
    onset: {
      type: String,
      enum: ['acute', 'chronic', 'gradual', 'sudden']
    },
    notes: String
  }],
  
  // Clinical Findings
  clinicalFindings: {
    physicalExamination: String,
    vitalSigns: {
      bloodPressure: {
        systolic: Number,
        diastolic: Number
      },
      heartRate: Number,
      temperature: Number,
      respiratoryRate: Number,
      oxygenSaturation: Number
    },
    systemicFindings: [{
      system: String,
      findings: String
    }]
  },
  
  // Diagnostic Tests
  diagnosticTests: [{
    testType: String,
    testName: String,
    result: String,
    normalRange: String,
    interpretation: String,
    date: Date,
    orderedBy: String
  }],
  
  // Treatment Plan
  treatmentPlan: {
    medications: [{
      name: String,
      dosage: String,
      frequency: String,
      duration: String,
      route: String,
      instructions: String
    }],
    procedures: [{
      name: String,
      scheduledDate: Date,
      urgency: {
        type: String,
        enum: ['routine', 'urgent', 'emergency']
      },
      notes: String
    }],
    lifestyle: [{
      recommendation: String,
      importance: {
        type: String,
        enum: ['low', 'medium', 'high']
      }
    }],
    followUp: {
      required: {
        type: Boolean,
        default: false
      },
      timeframe: String,
      instructions: String,
      scheduledDate: Date
    }
  },
  
  // Prognosis
  prognosis: {
    outlook: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor', 'guarded']
    },
    expectedRecoveryTime: String,
    complications: [{
      risk: String,
      probability: {
        type: String,
        enum: ['low', 'medium', 'high']
      },
      prevention: String
    }],
    notes: String
  },
  
  // Healthcare Provider Information
  diagnosedBy: {
    providerId: {
      type: String,
      required: true
    },
    providerName: {
      type: String,
      required: true
    },
    providerRole: {
      type: String,
      enum: ['doctor', 'nurse', 'medical_officer', 'specialist'],
      required: true
    },
    qualification: String,
    signature: String
  },
  
  // Facility Information
  facility: {
    facilityId: {
      type: String,
      required: true
    },
    facilityName: {
      type: String,
      required: true
    },
    facilityType: {
      type: String,
      enum: ['hospital', 'clinic', 'health_center', 'specialist_center'],
      required: true
    },
    department: String
  },
  
  // Location Information
  location: {
    state: {
      type: String,
      required: true
    },
    lga: String,
    ward: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // Diagnosis Metadata
  diagnosisDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  
  status: {
    type: String,
    enum: ['active', 'resolved', 'chronic', 'under_review'],
    default: 'active'
  },
  
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  category: {
    type: String,
    enum: ['infectious', 'chronic', 'acute', 'mental_health', 'maternal', 'pediatric', 'geriatric', 'other'],
    required: true
  },
  
  // Quality Assurance
  reviewed: {
    isReviewed: {
      type: Boolean,
      default: false
    },
    reviewedBy: String,
    reviewDate: Date,
    reviewNotes: String,
    approved: {
      type: Boolean,
      default: false
    }
  },
  
  // Sync Information
  deviceId: {
    type: String,
    required: true
  },
  
  syncStatus: {
    type: String,
    enum: ['pending', 'synced', 'failed'],
    default: 'pending'
  },
  
  lastSyncAttempt: Date,
  
  syncErrors: [{
    error: String,
    timestamp: Date,
    resolved: {
      type: Boolean,
      default: false
    }
  }],
  
  // Additional Information
  notes: String,
  
  attachments: [{
    filename: String,
    originalName: String,
    mimeType: String,
    size: Number,
    uploadDate: Date,
    description: String
  }],
  
  // Audit Trail
  createdBy: {
    type: String,
    required: true
  },
  
  updatedBy: String,
  
  version: {
    type: Number,
    default: 1
  }
}, {
  timestamps: true,
  versionKey: false
});

// Indexes for better query performance
diagnosisSchema.index({ patientId: 1, diagnosisDate: -1 });
diagnosisSchema.index({ 'diagnosedBy.providerId': 1, diagnosisDate: -1 });
diagnosisSchema.index({ 'facility.facilityId': 1, diagnosisDate: -1 });
diagnosisSchema.index({ 'location.state': 1, diagnosisDate: -1 });
diagnosisSchema.index({ category: 1, status: 1 });
diagnosisSchema.index({ priority: 1, diagnosisDate: -1 });
diagnosisSchema.index({ syncStatus: 1, lastSyncAttempt: 1 });

// Virtual for diagnosis age (time since diagnosis)
diagnosisSchema.virtual('diagnosisAge').get(function() {
  return Date.now() - this.diagnosisDate.getTime();
});

// Method to mark diagnosis as synced
diagnosisSchema.methods.markAsSynced = function() {
  this.syncStatus = 'synced';
  this.lastSyncAttempt = new Date();
  return this.save();
};

// Method to add sync error
diagnosisSchema.methods.addSyncError = function(error) {
  this.syncErrors.push({
    error: error,
    timestamp: new Date(),
    resolved: false
  });
  this.syncStatus = 'failed';
  this.lastSyncAttempt = new Date();
  return this.save();
};

// Static method to find diagnoses by patient
diagnosisSchema.statics.findByPatient = function(patientId) {
  return this.find({ patientId }).sort({ diagnosisDate: -1 });
};

// Static method to find diagnoses by provider
diagnosisSchema.statics.findByProvider = function(providerId) {
  return this.find({ 'diagnosedBy.providerId': providerId }).sort({ diagnosisDate: -1 });
};

// Static method to find diagnoses by facility
diagnosisSchema.statics.findByFacility = function(facilityId) {
  return this.find({ 'facility.facilityId': facilityId }).sort({ diagnosisDate: -1 });
};

// Static method to find pending sync diagnoses
diagnosisSchema.statics.findPendingSync = function() {
  return this.find({ syncStatus: 'pending' }).sort({ diagnosisDate: 1 });
};

// Static method to find by category
diagnosisSchema.statics.findByCategory = function(category) {
  return this.find({ category }).sort({ diagnosisDate: -1 });
};

module.exports = mongoose.model('Diagnosis', diagnosisSchema);
