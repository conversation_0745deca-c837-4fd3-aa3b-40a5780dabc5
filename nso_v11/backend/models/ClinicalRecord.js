const mongoose = require('mongoose');

const clinicalRecordSchema = new mongoose.Schema({
  // Patient Information
  patientId: {
    type: String,
    required: true,
    index: true
  },
  patientName: {
    type: String,
    required: true
  },
  patientAge: {
    type: Number,
    required: true
  },
  patientGender: {
    type: String,
    enum: ['male', 'female', 'other'],
    required: true
  },
  
  // Clinical Information
  diagnosis: {
    primary: {
      type: String,
      required: true
    },
    secondary: [{
      type: String
    }],
    icdCodes: [{
      code: String,
      description: String
    }]
  },
  
  symptoms: [{
    name: String,
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe']
    },
    duration: String,
    notes: String
  }],
  
  vitalSigns: {
    bloodPressure: {
      systolic: Number,
      diastolic: Number
    },
    heartRate: Number,
    temperature: Number,
    respiratoryRate: Number,
    oxygenSaturation: Number,
    weight: Number,
    height: Number
  },
  
  medications: [{
    name: String,
    dosage: String,
    frequency: String,
    duration: String,
    prescribedBy: String,
    notes: String
  }],
  
  procedures: [{
    name: String,
    date: Date,
    performedBy: String,
    notes: String,
    outcome: String
  }],
  
  labResults: [{
    testName: String,
    result: String,
    normalRange: String,
    date: Date,
    notes: String
  }],
  
  // Healthcare Provider Information
  providerId: {
    type: String,
    required: true,
    index: true
  },
  providerName: {
    type: String,
    required: true
  },
  providerRole: {
    type: String,
    enum: ['doctor', 'nurse', 'medical_officer', 'healthcare_worker'],
    required: true
  },
  
  // Facility Information
  facilityId: {
    type: String,
    required: true,
    index: true
  },
  facilityName: {
    type: String,
    required: true
  },
  facilityType: {
    type: String,
    enum: ['hospital', 'clinic', 'health_center', 'pharmacy'],
    required: true
  },
  
  // Location Information
  location: {
    state: String,
    lga: String,
    ward: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // Record Metadata
  recordType: {
    type: String,
    enum: ['consultation', 'admission', 'emergency', 'follow_up', 'discharge'],
    required: true
  },
  
  status: {
    type: String,
    enum: ['active', 'completed', 'cancelled'],
    default: 'active'
  },
  
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Timestamps
  consultationDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  
  followUpDate: {
    type: Date
  },
  
  // Sync Information
  deviceId: {
    type: String,
    required: true
  },
  
  syncStatus: {
    type: String,
    enum: ['pending', 'synced', 'failed'],
    default: 'pending'
  },
  
  lastSyncAttempt: {
    type: Date
  },
  
  syncErrors: [{
    error: String,
    timestamp: Date,
    resolved: {
      type: Boolean,
      default: false
    }
  }],
  
  // Additional Notes
  notes: {
    type: String
  },
  
  attachments: [{
    filename: String,
    originalName: String,
    mimeType: String,
    size: Number,
    uploadDate: Date,
    description: String
  }],
  
  // Audit Trail
  createdBy: {
    type: String,
    required: true
  },
  
  updatedBy: {
    type: String
  },
  
  version: {
    type: Number,
    default: 1
  }
}, {
  timestamps: true,
  versionKey: false
});

// Indexes for better query performance
clinicalRecordSchema.index({ patientId: 1, consultationDate: -1 });
clinicalRecordSchema.index({ providerId: 1, consultationDate: -1 });
clinicalRecordSchema.index({ facilityId: 1, consultationDate: -1 });
clinicalRecordSchema.index({ 'location.state': 1, consultationDate: -1 });
clinicalRecordSchema.index({ recordType: 1, status: 1 });
clinicalRecordSchema.index({ syncStatus: 1, lastSyncAttempt: 1 });

// Virtual for patient age calculation
clinicalRecordSchema.virtual('calculatedAge').get(function() {
  if (this.patientAge) {
    return this.patientAge;
  }
  // Could calculate from birth date if available
  return null;
});

// Method to mark record as synced
clinicalRecordSchema.methods.markAsSynced = function() {
  this.syncStatus = 'synced';
  this.lastSyncAttempt = new Date();
  return this.save();
};

// Method to add sync error
clinicalRecordSchema.methods.addSyncError = function(error) {
  this.syncErrors.push({
    error: error,
    timestamp: new Date(),
    resolved: false
  });
  this.syncStatus = 'failed';
  this.lastSyncAttempt = new Date();
  return this.save();
};

// Static method to find records by patient
clinicalRecordSchema.statics.findByPatient = function(patientId) {
  return this.find({ patientId }).sort({ consultationDate: -1 });
};

// Static method to find records by provider
clinicalRecordSchema.statics.findByProvider = function(providerId) {
  return this.find({ providerId }).sort({ consultationDate: -1 });
};

// Static method to find records by facility
clinicalRecordSchema.statics.findByFacility = function(facilityId) {
  return this.find({ facilityId }).sort({ consultationDate: -1 });
};

// Static method to find pending sync records
clinicalRecordSchema.statics.findPendingSync = function() {
  return this.find({ syncStatus: 'pending' }).sort({ consultationDate: 1 });
};

module.exports = mongoose.model('ClinicalRecord', clinicalRecordSchema);
