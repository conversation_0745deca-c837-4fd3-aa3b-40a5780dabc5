import { LinearGradient } from 'expo-linear-gradient';
import React, { useRef, useState } from 'react';
import {
    Alert,
    Animated,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    View,
} from 'react-native';
import { Colors, Spacing, Typography } from '../constants/theme';
import Button from './ui/Button';
import Dropdown from './ui/Dropdown';
import Input from './ui/Input';

interface RegistrationScreenProps {
  onRegistrationComplete: (userData: UserData) => void;
}

interface UserData {
  fullName: string;
  role: string;
  facility: string;
  state: string;
  contactInfo: string;
}

const roles = [
  { label: 'Doctor', value: 'doctor', icon: '👨‍⚕️' },
  { label: 'Nurse', value: 'nurse', icon: '👩‍⚕️' },
  { label: 'Community Health Worker', value: 'chw', icon: '🏥' },
  { label: 'Midwife', value: 'midwife', icon: '🤱' },
  { label: 'Medical Assistant', value: 'assistant', icon: '🩺' },
  { label: 'Other Healthcare Worker', value: 'other', icon: '📋' },
];

export default function RegistrationScreen({ onRegistrationComplete }: RegistrationScreenProps) {
  const [formData, setFormData] = useState<UserData>({
    fullName: '',
    role: '',
    facility: '',
    state: '',
    contactInfo: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleRegistration = async () => {
    // Validate form
    if (!formData.fullName.trim()) {
      Alert.alert('Error', 'Please enter your full name');
      return;
    }
    if (!formData.role.trim()) {
      Alert.alert('Error', 'Please select your role');
      return;
    }
    if (!formData.facility.trim()) {
      Alert.alert('Error', 'Please enter your facility');
      return;
    }
    if (!formData.state.trim()) {
      Alert.alert('Error', 'Please enter your state');
      return;
    }
    if (!formData.contactInfo.trim()) {
      Alert.alert('Error', 'Please enter your contact information');
      return;
    }

    setIsLoading(true);

    // Simulate registration process
    setTimeout(() => {
      setIsLoading(false);
      onRegistrationComplete(formData);
    }, 2000);
  };

  const updateFormData = (field: keyof UserData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary.dark} />
      <LinearGradient
        colors={Colors.background.gradient}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.iconContainer}>
                <View style={styles.icon}>
                  <Text style={styles.iconText}>👤</Text>
                </View>
              </View>
              
              <Text style={styles.title}>Create Your Profile</Text>
              <Text style={styles.subtitle}>
                Now let's set up your account. This information helps personalize 
                your experience and supports proper record-keeping.
              </Text>
            </View>

            {/* Registration Form */}
            <View style={styles.formContainer}>
              {/* Full Name */}
              <Input
                label="Full Name"
                value={formData.fullName}
                onChangeText={(text) => updateFormData('fullName', text)}
                placeholder="Enter your full name"
                autoCapitalize="words"
                editable={!isLoading}
                required
              />

              {/* Role */}
              <Dropdown
                label="Role"
                placeholder="Select your role"
                options={roles}
                value={formData.role}
                onSelect={(option) => updateFormData('role', option.value)}
                disabled={isLoading}
                required
              />

              {/* Facility */}
              <Input
                label="Facility"
                value={formData.facility}
                onChangeText={(text) => updateFormData('facility', text)}
                placeholder="Enter your facility name"
                autoCapitalize="words"
                editable={!isLoading}
                required
              />

              {/* State */}
              <Input
                label="State"
                value={formData.state}
                onChangeText={(text) => updateFormData('state', text)}
                placeholder="Enter your state"
                autoCapitalize="words"
                editable={!isLoading}
                required
              />

              {/* Contact Information */}
              <Input
                label="Contact Information"
                value={formData.contactInfo}
                onChangeText={(text) => updateFormData('contactInfo', text)}
                placeholder="Phone number or email"
                keyboardType="email-address"
                editable={!isLoading}
                required
              />

              {/* Registration Button */}
              <Button
                variant="primary"
                size="large"
                loading={isLoading}
                onPress={handleRegistration}
                fullWidth
                style={styles.registerButton}
              >
                Complete Registration
              </Button>

              {/* Privacy Note */}
              <Text style={styles.privacyText}>
                Your information is securely stored and will only be used for 
                healthcare service delivery and administrative purposes.
              </Text>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Decorative Elements */}
        <View style={styles.decorativeElements}>
          <View style={[styles.circle, styles.circle1]} />
          <View style={[styles.circle, styles.circle2]} />
        </View>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  iconContainer: {
    marginBottom: Spacing.lg,
  },
  icon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  iconText: {
    fontSize: Typography.fontSize['4xl'],
  },
  title: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.light,
    textAlign: 'center',
    marginBottom: Spacing.base,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.muted,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.base,
  },
  formContainer: {
    width: '100%',
  },
  registerButton: {
    marginTop: Spacing.base,
    marginBottom: Spacing.lg,
  },
  privacyText: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.muted,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.xs,
  },
  decorativeElements: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -1,
  },
  circle: {
    position: 'absolute',
    borderRadius: 1000,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  circle1: {
    width: 150,
    height: 150,
    top: -75,
    right: -75,
  },
  circle2: {
    width: 100,
    height: 100,
    bottom: -50,
    left: -50,
  },
});
