require('dotenv').config();

const config = {
  // Server Configuration
  PORT: process.env.PORT || 5000,
  NODE_ENV: process.env.NODE_ENV || 'development',

  // Database Configuration
  MONGODB_URL: process.env.MONGODB_URL || "mongodb://localhost:27017/nso_dev",

  // JWT Configuration
  JWT_SECRET: process.env.JWT_SECRET || 'nso-app-secret-key-2024',
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '7d',

  // Encryption Configuration
  ENCRYPTION_SECRET: process.env.ENCRYPTION_SECRET || 'nso-encryption-master-key-2024-secure',

  // API Configuration
  API_VERSION: '/api/v1',
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB

  // Rate Limiting
  RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: 100,

  // Sync Configuration
  SYNC_BATCH_SIZE: 50,
  SYNC_TIMEOUT: 30000, // 30 seconds

  // Logging
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',

  // CORS Configuration
  CORS_ORIGINS: process.env.CORS_ORIGINS === '*' ? '*' : (process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : ['http://localhost:8082', 'exp://*************:8082']),

  // Security
  BCRYPT_ROUNDS: 12,

  // File Upload
  UPLOAD_PATH: './uploads',
  ALLOWED_FILE_TYPES: ['image/jpeg', 'image/png', 'application/pdf'],
};

module.exports = config;