# NSO Clinical Assistant - UI/UX Design System

## 🎨 Design Foundation

### Color Palette
```
Primary Colors:
- Medical Blue: #2196F3 (Primary actions, headers)
- Clinical Green: #4CAF50 (Success, sync status)
- Clean White: #FFFFFF (Backgrounds, cards)
- Medical Gray: #F5F7FA (Secondary backgrounds)

Secondary Colors:
- Warning Orange: #FF9800 (Alerts, pending actions)
- Error Red: #F44336 (Errors, critical alerts)
- Info Cyan: #00BCD4 (Information, tips)
- Offline Gray: #9E9E9E (Offline indicators)

Text Colors:
- Primary Text: #212121 (Main content)
- Secondary Text: #757575 (Descriptions, labels)
- Light Text: #FFFFFF (On colored backgrounds)
- Disabled Text: #BDBDBD (Inactive elements)

Dark Mode:
- Dark Background: #121212
- Dark Surface: #1E1E1E
- Dark Primary: #BB86FC
- Dark Text: #FFFFFF
```

### Typography
```
Font Family: 'Roboto' (Primary), 'Open Sans' (Secondary)

Hierarchy:
- H1 (Page Titles): 28px, Bold, #212121
- H2 (Section Headers): 24px, Medium, #212121
- H3 (Card Titles): 20px, Medium, #212121
- Body Large: 16px, Regular, #212121
- Body Medium: 14px, Regular, #757575
- Body Small: 12px, Regular, #757575
- Button Text: 16px, Medium, #FFFFFF
- Caption: 11px, Regular, #9E9E9E
```

### Spacing System
```
Base Unit: 8px

Spacing Scale:
- xs: 4px (tight spacing)
- sm: 8px (small spacing)
- md: 16px (medium spacing)
- lg: 24px (large spacing)
- xl: 32px (extra large spacing)
- xxl: 48px (section spacing)

Component Spacing:
- Card Padding: 16px
- Button Padding: 12px vertical, 24px horizontal
- Input Padding: 16px
- Screen Margins: 16px
- Section Gaps: 24px
```

### Component Styles

#### Buttons
```
Primary Button:
- Background: #2196F3
- Text: #FFFFFF, 16px, Medium
- Border Radius: 8px
- Height: 48px
- Shadow: 0 2px 8px rgba(33, 150, 243, 0.3)
- Pressed: #1976D2

Secondary Button:
- Background: Transparent
- Border: 2px solid #2196F3
- Text: #2196F3, 16px, Medium
- Border Radius: 8px
- Height: 48px

Danger Button:
- Background: #F44336
- Text: #FFFFFF, 16px, Medium
- Border Radius: 8px
- Height: 48px
```

#### Cards
```
Standard Card:
- Background: #FFFFFF
- Border Radius: 12px
- Shadow: 0 2px 12px rgba(0, 0, 0, 0.08)
- Padding: 16px
- Border: 1px solid #E0E0E0

Elevated Card:
- Background: #FFFFFF
- Border Radius: 12px
- Shadow: 0 4px 20px rgba(0, 0, 0, 0.12)
- Padding: 20px
```

#### Input Fields
```
Text Input:
- Background: #FFFFFF
- Border: 2px solid #E0E0E0
- Border Radius: 8px
- Height: 48px
- Padding: 16px
- Focus Border: #2196F3
- Error Border: #F44336

Dropdown:
- Background: #FFFFFF
- Border: 2px solid #E0E0E0
- Border Radius: 8px
- Height: 48px
- Padding: 16px
- Arrow Icon: #757575
```

### Icons
```
Icon Library: Material Design Icons
Size: 24px (standard), 20px (small), 32px (large)
Color: #757575 (inactive), #2196F3 (active), #FFFFFF (on colored)

Medical Icons:
- Stethoscope: diagnosis
- User-md: doctor/healthcare
- Hospital: facility
- Heart-pulse: vital signs
- Clipboard-check: completed
- Upload-cloud: sync
- Shield-check: security
- Map-marker: location
```

## 📱 Screen Layouts

### Layout Grid
```
Screen Width: 360dp (base), responsive up to 480dp
Margins: 16dp left/right
Columns: 12-column grid system
Gutters: 8dp between columns

Safe Areas:
- Top: Status bar + 16dp
- Bottom: Navigation bar + 16dp
- Sides: 16dp minimum
```

### Navigation Patterns
```
Primary Navigation:
- Bottom Tab Bar (Dashboard, History, Settings)
- Height: 64dp
- Icons: 24dp
- Labels: 12px

Secondary Navigation:
- Top App Bar with back button
- Height: 56dp
- Title: 20px Medium
- Actions: 24dp icons

Modal Navigation:
- Full screen overlays
- Slide up animation
- Close button top-right
```

## 🔄 Interaction Patterns

### Animations
```
Transitions:
- Screen transitions: 300ms ease-out
- Button press: 150ms ease-in-out
- Card hover: 200ms ease-out
- Loading states: Continuous rotation

Feedback:
- Button press: Scale 0.95, opacity 0.8
- Card tap: Elevation increase
- Success: Green checkmark animation
- Error: Red shake animation
```

### Loading States
```
Skeleton Loading:
- Gray placeholder blocks
- Shimmer animation
- Maintain layout structure

Spinner Loading:
- Circular progress indicator
- 32dp size
- Primary color (#2196F3)

Progress Loading:
- Linear progress bar
- Height: 4dp
- Primary color fill
```

### Status Indicators
```
Network Status:
- Online: Green dot + "Online"
- Offline: Gray dot + "Offline"
- Syncing: Animated blue dot + "Syncing..."

Sync Status:
- Synced: Green checkmark
- Pending: Orange clock icon
- Failed: Red exclamation

Data Status:
- Saved: Green "Saved locally"
- Uploading: Blue "Uploading..."
- Error: Red "Upload failed"
```

## 🛡️ Security Visual Elements

### Activation Key Input
```
Visual Design:
- Monospace font for key display
- Character grouping (XXXX-XXXX-XXXX)
- Secure input masking option
- Copy/paste protection
- Visual validation feedback
```

### Device Lock Indicator
```
Lock Status:
- Locked: Green shield icon + "Device Secured"
- Unlocked: Orange shield + "Activation Required"
- Error: Red shield + "Security Error"
```

### Data Encryption Status
```
Encryption Indicator:
- Small lock icon next to sensitive data
- "Encrypted" badge on data cards
- Security level indicators
```

## 📊 Data Visualization

### Diagnosis Cards
```
Card Layout:
- Patient info header
- Complaint summary
- Timestamp and location
- Sync status badge
- Action buttons (Edit, View, Delete)

Status Colors:
- Completed: Green border
- Draft: Orange border
- Synced: Blue checkmark
- Pending: Gray clock
```

### Statistics Dashboard
```
Metrics Cards:
- Total diagnoses count
- Sync success rate
- Last sync time
- Offline time duration

Chart Elements:
- Simple bar charts
- Donut charts for percentages
- Timeline for sync history
```

## 🌙 Dark Mode Adaptations

### Color Adjustments
```
Dark Theme Colors:
- Background: #121212
- Surface: #1E1E1E
- Primary: #BB86FC
- Secondary: #03DAC6
- Error: #CF6679
- Text: #FFFFFF
- Text Secondary: #B3B3B3
```

### Component Adaptations
```
Cards: Dark surface with subtle borders
Buttons: Adjusted contrast ratios
Inputs: Dark backgrounds with light borders
Icons: Light colors for visibility
Shadows: Reduced or eliminated
```

This design system ensures consistency, accessibility, and professional medical aesthetics throughout the NSO Clinical Assistant application.
