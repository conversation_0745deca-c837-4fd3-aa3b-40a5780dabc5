# NSO Clinical Assistant - Diagnosis Screens Design

## 🧪 New Diagnosis Screen

```
┌─────────────────────────────────────┐
│ ●●●                            ●●● │ Status Bar
├─────────────────────────────────────┤
│  ←        Begin Diagnosis       ⋮   │ Back, Title, Menu
│                                     │
│    Step 1 of 4                     │ Progress indicator
│  ████████░░░░░░░░░░░░░░░░░░░░░░░░   │ Progress bar 25%
│                                     │
│         Patient Information         │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Patient Age *               │   │ Required field
│  │ ┌─────────────────────────┐ │   │
│  │ │ 5 years                 │ │   │ Number input
│  │ └─────────────────────────┘ │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Gender *                    │   │ Radio buttons
│  │ ○ Male    ○ Female         │   │
│  │ ○ Other   ○ Prefer not to  │   │
│  │           say              │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Chief Complaint *           │   │ Dropdown
│  │ ┌─────────────────────────┐ │   │
│  │ │ Fever and Cough      ▼  │ │   │ Selected option
│  │ └─────────────────────────┘ │   │
│  └─────────────────────────────┘   │
│                                     │
│                                     │
│  ┌─────────────────────────────┐   │
│  │           Next              │   │ Primary Button
│  └─────────────────────────────┘   │ Enabled when valid
│                                     │
│        Save as Draft                │ Text button, center
│                                     │
└─────────────────────────────────────┘
```

---

## 🔍 Clinical Findings Screen (Step 2)

```
┌─────────────────────────────────────┐
│  ←      Clinical Findings       ⋮   │ Back, Title, Menu
│                                     │
│    Step 2 of 4                     │ Progress indicator
│  ████████████████░░░░░░░░░░░░░░░░   │ Progress bar 50%
│                                     │
│      Fever and Cough Assessment    │ H2, Based on complaint
│                                     │
│         Vital Signs                 │ H3, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Temperature: 38.5°C         │   │ Vital input
│  │ Heart Rate: 95 bpm          │   │ Quick entry
│  │ Respiratory Rate: 22/min    │   │ fields
│  └─────────────────────────────┘   │
│                                     │
│         Clinical Signs              │ H3, #212121
│                                     │
│  ☑️ Runny nose                     │ Checkbox selected
│  ☑️ Sore throat                    │ Checkbox selected
│  ☐ Difficulty breathing            │ Checkbox unselected
│  ☐ Chest pain                      │ Checkbox unselected
│  ☑️ Fatigue/weakness               │ Checkbox selected
│  ☐ Nausea/vomiting                 │ Checkbox unselected
│  ☐ Headache                        │ Checkbox unselected
│                                     │
│         Additional Notes            │ H3, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Patient reports symptoms    │   │ Text area
│  │ started 2 days ago. No      │   │ Optional notes
│  │ recent travel history.      │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │           Next              │   │ Primary Button
│  └─────────────────────────────┘   │
│                                     │
│    ← Back        Save as Draft      │ Navigation options
│                                     │
└─────────────────────────────────────┘
```

---

## 🎯 Diagnosis Summary Screen (Step 3)

```
┌─────────────────────────────────────┐
│  ←      Diagnosis Summary       ⋮   │ Back, Title, Menu
│                                     │
│    Step 3 of 4                     │ Progress indicator
│  ████████████████████████░░░░░░░░   │ Progress bar 75%
│                                     │
│      AI-Generated Assessment       │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 🤖 Suggested Diagnosis      │   │ AI suggestion card
│  │                             │   │ Light blue background
│  │ Upper Respiratory Tract     │   │ #E3F2FD
│  │ Infection (Common Cold)     │   │
│  │                             │   │
│  │ Confidence: 85% ████████▒▒  │   │ Confidence bar
│  │                             │   │
│  │ 💡 Based on: Fever, cough,  │   │ Reasoning
│  │    runny nose, sore throat  │   │
│  └─────────────────────────────┘   │
│                                     │
│      Manual Override               │ H3, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Custom Diagnosis            │   │ Text input
│  │ ┌─────────────────────────┐ │   │ Optional override
│  │ │                         │ │   │
│  │ └─────────────────────────┘ │   │
│  └─────────────────────────────┘   │
│                                     │
│      Treatment Recommendations     │ H3, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 💊 Rest and hydration       │   │ Treatment card
│  │ 🌡️ Monitor temperature      │   │ Light green background
│  │ 🏥 Return if symptoms       │   │ #E8F5E8
│  │    worsen or persist >7days │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │           Next              │   │ Primary Button
│  └─────────────────────────────┘   │
│                                     │
│    ← Back        Save as Draft      │ Navigation options
│                                     │
└─────────────────────────────────────┘
```

---

## ✅ Diagnosis Completion Screen (Step 4)

```
┌─────────────────────────────────────┐
│  ←       Complete Diagnosis     ⋮   │ Back, Title, Menu
│                                     │
│    Step 4 of 4                     │ Progress indicator
│  ████████████████████████████████   │ Progress bar 100%
│                                     │
│         Review & Submit             │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 👤 Patient: Child, 5 years  │   │ Summary card
│  │ 🩺 Complaint: Fever & Cough │   │ White background
│  │ 📋 Diagnosis: Upper Resp.   │   │ with border
│  │    Tract Infection          │   │
│  │ 📍 Location: Lagos, Nigeria │   │ Auto-captured
│  │ 🕐 Time: 14:30, Mar 15      │   │ Timestamp
│  └─────────────────────────────┘   │
│                                     │
│      Data Collection Notice        │ H3, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ ℹ️ This diagnosis will be:   │   │ Info card
│  │                             │   │ Light blue background
│  │ • Saved locally on device   │   │ #E3F2FD
│  │ • Synced to admin portal    │   │
│  │ • Include GPS location      │   │
│  │ • Timestamped securely      │   │
│  └─────────────────────────────┘   │
│                                     │
│  ☑️ I confirm this diagnosis is     │ Confirmation checkbox
│     accurate and complete          │ Required
│                                     │
│  ┌─────────────────────────────┐   │
│  │      Save Diagnosis         │   │ Primary Button
│  └─────────────────────────────┘   │ Enabled when confirmed
│                                     │
│    ← Back        Save as Draft      │ Navigation options
│                                     │
└─────────────────────────────────────┘
```

---

## ✅ Diagnosis Success Screen

```
┌─────────────────────────────────────┐
│                                     │
│                                     │
│    [ANIMATED SUCCESS CHECKMARK]     │ Large green checkmark
│                                     │ 120dp, animated
│                                     │
│      Diagnosis Saved!               │ H1, Center, #4CAF50
│                                     │
│   Your clinical assessment has      │ Body Large, Center
│   been recorded successfully.       │ #757575
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 📱 Saved locally            │   │ Status indicators
│  │ 📤 Queued for sync          │   │ Light backgrounds
│  │ 🔒 Encrypted & secure       │   │ with icons
│  └─────────────────────────────┘   │
│                                     │
│      Next Steps                     │ H3, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │    New Diagnosis            │   │ Primary Button
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │    View History             │   │ Secondary Button
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │    Back to Dashboard        │   │ Text Button
│  └─────────────────────────────┘   │
│                                     │
└─────────────────────────────────────┘
```

---

## 🎨 Diagnosis Screen Design Specifications

### Progress Indicator
```
Progress Bar:
- Height: 4dp
- Background: #E0E0E0
- Fill: #2196F3
- Border Radius: 2dp
- Animation: Smooth fill transition

Step Counter:
- Font: 14px medium
- Color: #757575
- Position: Above progress bar
- Format: "Step X of Y"
```

### Form Field Styling
```
Input Fields:
- Height: 56dp
- Border: 2dp solid #E0E0E0
- Border Radius: 8dp
- Padding: 16dp
- Focus: Border #2196F3

Checkboxes:
- Size: 24dp
- Unchecked: Border #757575
- Checked: Background #2196F3
- Checkmark: White
- Label: 16px regular, 8dp margin

Radio Buttons:
- Size: 20dp
- Unchecked: Border #757575
- Checked: Fill #2196F3
- Label: 16px regular, 8dp margin
```

### AI Suggestion Card
```
Card Design:
- Background: #E3F2FD (light blue)
- Border: 1dp solid #BBDEFB
- Border Radius: 12dp
- Padding: 16dp
- Icon: 32dp robot emoji

Confidence Bar:
- Height: 8dp
- Background: #E0E0E0
- Fill: Gradient #4CAF50 to #FF9800
- Border Radius: 4dp
- Percentage: 14px medium, right aligned
```

### Treatment Recommendations
```
Card Design:
- Background: #E8F5E8 (light green)
- Border: 1dp solid #C8E6C9
- Border Radius: 12dp
- Padding: 16dp

List Items:
- Icon: 24dp medical icons
- Text: 14px regular
- Spacing: 8dp between items
- Color: #2E7D32 (dark green)
```

This diagnosis flow design ensures clinical accuracy while maintaining ease of use for healthcare professionals in field conditions.
