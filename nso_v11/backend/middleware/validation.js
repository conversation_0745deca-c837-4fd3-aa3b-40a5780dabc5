const Joi = require('joi');
const logger = require('../utils/logger');

/**
 * Validation middleware factory
 * Creates middleware that validates request body against a schema
 */
const validate = (schema, options = {}) => {
  return (req, res, next) => {
    try {
      // Convert simple schema to Joi schema if needed
      const joiSchema = convertToJoiSchema(schema);
      
      // Validate request body
      const { error, value } = joiSchema.validate(req.body, {
        abortEarly: false,
        allowUnknown: options.allowUnknown || false,
        stripUnknown: options.stripUnknown || true,
        ...options
      });
      
      if (error) {
        const validationErrors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));
        
        logger.warn('Validation failed:', { 
          path: req.path, 
          errors: validationErrors 
        });
        
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validationErrors,
          timestamp: new Date().toISOString()
        });
      }
      
      // Replace request body with validated and sanitized data
      req.body = value;
      next();
      
    } catch (err) {
      logger.error('Validation middleware error:', err);
      res.status(500).json({
        success: false,
        error: 'Internal validation error',
        code: 'VALIDATION_INTERNAL_ERROR'
      });
    }
  };
};

/**
 * Validate query parameters
 */
const validateQuery = (schema, options = {}) => {
  return (req, res, next) => {
    try {
      const joiSchema = convertToJoiSchema(schema);
      
      const { error, value } = joiSchema.validate(req.query, {
        abortEarly: false,
        allowUnknown: options.allowUnknown || true,
        stripUnknown: options.stripUnknown || false,
        ...options
      });
      
      if (error) {
        const validationErrors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));
        
        return res.status(400).json({
          success: false,
          error: 'Query validation failed',
          code: 'QUERY_VALIDATION_ERROR',
          details: validationErrors
        });
      }
      
      req.query = value;
      next();
      
    } catch (err) {
      logger.error('Query validation error:', err);
      res.status(500).json({
        success: false,
        error: 'Internal query validation error',
        code: 'QUERY_VALIDATION_INTERNAL_ERROR'
      });
    }
  };
};

/**
 * Validate URL parameters
 */
const validateParams = (schema, options = {}) => {
  return (req, res, next) => {
    try {
      const joiSchema = convertToJoiSchema(schema);
      
      const { error, value } = joiSchema.validate(req.params, {
        abortEarly: false,
        allowUnknown: options.allowUnknown || false,
        stripUnknown: options.stripUnknown || true,
        ...options
      });
      
      if (error) {
        const validationErrors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));
        
        return res.status(400).json({
          success: false,
          error: 'Parameter validation failed',
          code: 'PARAM_VALIDATION_ERROR',
          details: validationErrors
        });
      }
      
      req.params = value;
      next();
      
    } catch (err) {
      logger.error('Parameter validation error:', err);
      res.status(500).json({
        success: false,
        error: 'Internal parameter validation error',
        code: 'PARAM_VALIDATION_INTERNAL_ERROR'
      });
    }
  };
};

/**
 * Convert simple schema object to Joi schema
 */
function convertToJoiSchema(schema) {
  if (schema.isJoi) {
    return schema; // Already a Joi schema
  }
  
  const joiObject = {};
  
  for (const [key, rules] of Object.entries(schema)) {
    joiObject[key] = createJoiField(rules);
  }
  
  return Joi.object(joiObject);
}

/**
 * Create Joi field from simple rules
 */
function createJoiField(rules) {
  let field;
  
  // Handle different rule formats
  if (typeof rules === 'string') {
    rules = { type: rules };
  }
  
  // Create base field based on type
  switch (rules.type) {
    case 'string':
      field = Joi.string();
      break;
    case 'number':
      field = Joi.number();
      break;
    case 'boolean':
      field = Joi.boolean();
      break;
    case 'array':
      field = Joi.array();
      if (rules.items) {
        field = field.items(createJoiField(rules.items));
      }
      break;
    case 'object':
      field = Joi.object();
      if (rules.properties) {
        const properties = {};
        for (const [key, prop] of Object.entries(rules.properties)) {
          properties[key] = createJoiField(prop);
        }
        field = field.keys(properties);
      }
      break;
    case 'date':
      field = Joi.date();
      break;
    case 'email':
      field = Joi.string().email();
      break;
    case 'uuid':
      field = Joi.string().uuid();
      break;
    default:
      field = Joi.any();
  }
  
  // Apply additional rules
  if (rules.required) {
    field = field.required();
  }
  
  if (rules.optional) {
    field = field.optional();
  }
  
  if (rules.min !== undefined) {
    field = field.min(rules.min);
  }
  
  if (rules.max !== undefined) {
    field = field.max(rules.max);
  }
  
  if (rules.enum) {
    field = field.valid(...rules.enum);
  }
  
  if (rules.pattern) {
    field = field.pattern(new RegExp(rules.pattern));
  }
  
  if (rules.default !== undefined) {
    field = field.default(rules.default);
  }
  
  if (rules.allow) {
    field = field.allow(...(Array.isArray(rules.allow) ? rules.allow : [rules.allow]));
  }
  
  if (rules.forbidden) {
    field = field.forbidden();
  }
  
  if (rules.custom) {
    field = field.custom(rules.custom);
  }
  
  return field;
}

/**
 * Common validation schemas
 */
const commonSchemas = {
  pagination: {
    limit: { type: 'number', min: 1, max: 1000, default: 20 },
    offset: { type: 'number', min: 0, default: 0 },
    page: { type: 'number', min: 1, default: 1 }
  },
  
  dateRange: {
    startDate: { type: 'date', optional: true },
    endDate: { type: 'date', optional: true }
  },
  
  deviceInfo: {
    deviceId: { type: 'string', required: true },
    platform: { type: 'string', enum: ['ios', 'android', 'web'] },
    osVersion: { type: 'string', optional: true },
    appVersion: { type: 'string', optional: true },
    deviceModel: { type: 'string', optional: true }
  },
  
  userIdentification: {
    userId: { type: 'string', required: true },
    deviceId: { type: 'string', required: true }
  }
};

/**
 * Sanitize input data
 */
const sanitize = (data) => {
  if (typeof data === 'string') {
    return data.trim();
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitize);
  }
  
  if (data && typeof data === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitize(value);
    }
    return sanitized;
  }
  
  return data;
};

module.exports = {
  validate,
  validateQuery,
  validateParams,
  commonSchemas,
  sanitize
};
