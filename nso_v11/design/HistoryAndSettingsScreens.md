# NSO Clinical Assistant - History & Settings Screens Design

## 📚 Diagnosis History Screen

```
┌─────────────────────────────────────┐
│ ●●●                            ●●● │ Status Bar
├─────────────────────────────────────┤
│  ←        Diagnosis History     🔍  │ Back, Title, Search
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 📊 Total: 47 diagnoses      │   │ Summary card
│  │ ✅ Synced: 45 • ⏳ Pending: 2│   │ Status overview
│  └─────────────────────────────┘   │
│                                     │
│  🔽 Filter & Sort               ▲   │ Collapsible filter
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 🩺 Fever & Cough            │   │ Diagnosis Card 1
│  │    Child, 5y • Mar 15, 2:30p│   │ Patient & timestamp
│  │    📍 2.3km from clinic     │   │ Location info
│  │    Upper Respiratory Tract  │   │ Diagnosis
│  │                      ✅     │   │ Sync status
│  │  👁️ View  ✏️ Edit  🗑️ Delete │   │ Action buttons
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 🩺 Headache & Nausea        │   │ Diagnosis Card 2
│  │    Adult, 32y • Mar 14, 9:15a│   │
│  │    📍 1.8km from clinic     │   │
│  │    Migraine                 │   │
│  │                      ⏳     │   │ Pending sync
│  │  👁️ View  ✏️ Edit  🗑️ Delete │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 🩺 Stomach Pain             │   │ Diagnosis Card 3
│  │    Teen, 16y • Mar 13, 4:45p│   │
│  │    📍 0.5km from clinic     │   │
│  │    Gastritis                │   │
│  │                      ✅     │   │ Synced
│  │  👁️ View  ✏️ Edit  🗑️ Delete │   │
│  └─────────────────────────────┘   │
│                                     │
│           Load More ↓               │ Pagination
│                                     │
└─────────────────────────────────────┘
```

---

## 🔍 Filter & Sort Panel (Expanded)

```
┌─────────────────────────────────────┐
│  🔽 Filter & Sort               ▼   │ Expanded state
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Date Range                  │   │ Filter section
│  │ ○ Today  ○ This Week        │   │ Radio options
│  │ ○ This Month  ● All Time    │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Sync Status                 │   │ Filter section
│  │ ☑️ Synced  ☑️ Pending       │   │ Checkboxes
│  │ ☐ Failed  ☐ Draft          │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Sort By                     │   │ Sort section
│  │ ● Newest First              │   │ Radio options
│  │ ○ Oldest First              │   │
│  │ ○ Distance                  │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │        Apply Filters        │   │ Primary button
│  └─────────────────────────────┘   │
│                                     │
└─────────────────────────────────────┘
```

---

## 👁️ Diagnosis Detail View

```
┌─────────────────────────────────────┐
│  ←      Diagnosis Details       ⋮   │ Back, Title, Menu
│                                     │
│         Patient Information         │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 👤 Age: 5 years             │   │ Patient details
│  │ ⚧️ Gender: Female            │   │ card
│  │ 📅 Date: Mar 15, 2024       │   │
│  │ 🕐 Time: 2:30 PM            │   │
│  │ 📍 Location: Lagos, Nigeria │   │
│  └─────────────────────────────┘   │
│                                     │
│         Clinical Assessment         │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Chief Complaint:            │   │ Assessment
│  │ Fever and Cough             │   │ details card
│  │                             │   │
│  │ Vital Signs:                │   │
│  │ • Temperature: 38.5°C       │   │
│  │ • Heart Rate: 95 bpm        │   │
│  │ • Respiratory: 22/min       │   │
│  │                             │   │
│  │ Clinical Signs:             │   │
│  │ ✓ Runny nose               │   │
│  │ ✓ Sore throat              │   │
│  │ ✓ Fatigue/weakness         │   │
│  └─────────────────────────────┘   │
│                                     │
│         Diagnosis & Treatment       │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Diagnosis:                  │   │ Treatment
│  │ Upper Respiratory Tract     │   │ details card
│  │ Infection (Common Cold)     │   │
│  │                             │   │
│  │ Treatment:                  │   │
│  │ • Rest and hydration        │   │
│  │ • Monitor temperature       │   │
│  │ • Return if symptoms worsen │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │         Edit                │   │ Primary button
│  └─────────────────────────────┘   │
│                                     │
│        Share Report                 │ Text button
│                                     │
└─────────────────────────────────────┘
```

---

## 📤 Upload Logs / Sync Screen

```
┌─────────────────────────────────────┐
│  ←         Upload Logs          🔄  │ Back, Title, Refresh
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 🌐 Network: Online          │   │ Status card
│  │ 📊 Queue: 3 pending uploads │   │ Current state
│  │ 🕐 Last sync: 5 minutes ago │   │
│  └─────────────────────────────┘   │
│                                     │
│         Pending Uploads             │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 📋 Fever & Cough           │   │ Pending item 1
│  │    Child, 5y • 2.3MB       │   │ Size info
│  │    ⏳ Waiting to upload     │   │ Status
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 📋 Headache & Nausea       │   │ Pending item 2
│  │    Adult, 32y • 1.8MB      │   │
│  │    🔄 Uploading... 45%      │   │ Progress
│  │    ████████████░░░░░░░░░░   │   │ Progress bar
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 📋 Stomach Pain            │   │ Pending item 3
│  │    Teen, 16y • 2.1MB       │   │
│  │    ❌ Upload failed         │   │ Error status
│  │       Tap to retry          │   │ Retry option
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │      Sync All Now           │   │ Primary button
│  └─────────────────────────────┘   │
│                                     │
│         Recent Uploads              │ H3, #757575
│                                     │
│  ✅ Migraine diagnosis - 2h ago     │ Success items
│  ✅ Hypertension check - 4h ago     │ Compact list
│  ✅ Diabetes follow-up - 1d ago     │
│                                     │
└─────────────────────────────────────┘
```

---

## ⚙️ Settings Screen

```
┌─────────────────────────────────────┐
│  ←           Settings           ⋮   │ Back, Title, Menu
│                                     │
│         Account                     │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ 👤 Dr. Sarah Johnson        │   │ Profile section
│  │    Community Health Worker  │   │ Role display
│  │    Central Health Clinic    │   │ Facility
│  │                      Edit → │   │ Edit option
│  └─────────────────────────────┘   │
│                                     │
│         Sync & Data                 │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Background Sync        ●    │   │ Toggle switch ON
│  │ Auto-upload when online     │   │ Description
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Data Usage                  │   │ Data info
│  │ 47 diagnoses • 125MB used   │   │ Statistics
│  │                    View →   │   │ Details link
│  └─────────────────────────────┘   │
│                                     │
│         Appearance                  │ H2, #212121
│                                     │
│  ┌─────────────────────────────┐   │
│  │ Dark Mode             ○     │   │ Toggle switch OFF
│  │ Easier on the eyes          │   │ Description
│  └─────────────────────────────┘   │
│                                     │
│         Support                     │ H2, #212121
│                                     │
│  📱 App Version: 2.1.4              │ Version info
│  ❓ Help & Documentation →          │ Help link
│  📞 Contact Admin Support →         │ Support link
│  🔒 Privacy Policy →                │ Legal link
│                                     │
│         Danger Zone                 │ H2, #F44336
│                                     │
│  ┌─────────────────────────────┐   │
│  │      Clear Local Data       │   │ Danger button
│  └─────────────────────────────┘   │ Red background
│                                     │
│  ┌─────────────────────────────┐   │
│  │         Logout              │   │ Secondary button
│  └─────────────────────────────┘   │
│                                     │
└─────────────────────────────────────┘
```

---

## 🎨 History & Settings Design Specifications

### History Card Design
```
Card Layout:
- Height: 120dp (collapsed), 160dp (expanded)
- Padding: 16dp
- Border Radius: 12dp
- Background: White
- Border: 1dp solid #E0E0E0
- Margin: 8dp vertical

Status Indicators:
- Synced: Green checkmark (✅) #4CAF50
- Pending: Orange hourglass (⏳) #FF9800
- Failed: Red X (❌) #F44336
- Draft: Gray pencil (📝) #9E9E9E

Action Buttons:
- Size: 32dp x 32dp
- Border Radius: 16dp
- Background: #F5F7FA
- Icon: 16dp, #757575
- Spacing: 8dp between buttons
```

### Filter Panel Animation
```
Expand Animation:
- Duration: 300ms ease-out
- Height: 0dp to 200dp
- Opacity: 0 to 1
- Content: Staggered fade-in

Collapse Animation:
- Duration: 200ms ease-in
- Height: 200dp to 0dp
- Opacity: 1 to 0
- Content: Immediate fade-out
```

### Settings Toggle Design
```
Toggle Switch:
- Width: 48dp
- Height: 24dp
- Track: #E0E0E0 (off), #2196F3 (on)
- Thumb: 20dp circle, white
- Animation: 200ms ease-in-out

Section Headers:
- Font: 18px medium
- Color: #212121
- Margin: 24dp top, 16dp bottom
- Divider: 1dp solid #E0E0E0
```

This design ensures efficient data management and user control while maintaining the professional medical aesthetic.
