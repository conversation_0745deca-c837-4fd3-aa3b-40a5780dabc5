2025-08-11 12:30:50:3050 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 12:30:50 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 132234,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 93405184,
      "heapTotal": 58404864,
      "heapUsed": 29279472,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      1.93,
      2.29,
      3.54
    ],
    "uptime": 163416.47
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
2025-08-11 12:32:04:324 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 12:32:04 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 132430,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 95997952,
      "heapTotal": 58929152,
      "heapUsed": 26678952,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      2.24,
      2.27,
      3.44
    ],
    "uptime": 163490.92
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
2025-08-11 14:07:42:742 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 14:07:42 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 137900,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94318592,
      "heapTotal": 58404864,
      "heapUsed": 29047328,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      5.75,
      6.24,
      5.15
    ],
    "uptime": 169228.88
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
2025-08-11 14:15:00:150 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 14:15:00 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 138863,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 96657408,
      "heapTotal": 59191296,
      "heapUsed": 26612272,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      3.36,
      4.02,
      4.52
    ],
    "uptime": 169666.29
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
2025-08-11 14:15:22:1522 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 14:15:22 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 138933,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 95842304,
      "heapTotal": 58875904,
      "heapUsed": 28104304,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      4.51,
      4.23,
      4.58
    ],
    "uptime": 169688.51
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
2025-08-11 14:18:39:1839 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 14:18:39 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 139396,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 94576640,
      "heapTotal": 58142720,
      "heapUsed": 29033288,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      4.45,
      4.43,
      4.61
    ],
    "uptime": 169885.18
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
2025-08-11 14:19:18:1918 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 14:19:18 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 139558,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 96600064,
      "heapTotal": 59191296,
      "heapUsed": 26461848,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      3.31,
      4.14,
      4.5
    ],
    "uptime": 169924.05
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
2025-08-11 14:22:33:2233 [[31merror[39m]: [31muncaughtException: Router.use() requires a middleware function but got a Object[39m
[31mTypeError: Router.use() requires a middleware function but got a Object[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)[39m
[31m    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)[39m
[31m    at Array.forEach (<anonymous>)[39m
[31m    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)[39m
[31m    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1562:14)[39m
[31m    at Object..js (node:internal/modules/cjs/loader:1699:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1313:32)[39m
[31m    at Function._load (node:internal/modules/cjs/loader:1123:12)[39m
[31m    at TracingChannel.traceSync (node:diagnostics_channel:322:14)[39m
TypeError: Router.use() requires a middleware function but got a Object
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js:469:13)
    at Function.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:227:21)
    at Array.forEach (<anonymous>)
    at Function.use (/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js:224:7)
    at Object.<anonymous> (/home/<USER>/Desktop/nso_v11/backend/server.js:106:5)
    at Module._compile (node:internal/modules/cjs/loader:1562:14)
    at Object..js (node:internal/modules/cjs/loader:1699:10)
    at Module.load (node:internal/modules/cjs/loader:1313:32)
    at Function._load (node:internal/modules/cjs/loader:1123:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
{
  "error": {},
  "exception": true,
  "date": "Mon Aug 11 2025 14:22:33 GMT+0100 (West Africa Standard Time)",
  "process": {
    "pid": 140064,
    "uid": 1000,
    "gid": 1000,
    "cwd": "/home/<USER>/Desktop/nso_v11/backend",
    "execPath": "/usr/local/node-v22.13.1-linux-x64/bin/node",
    "version": "v22.13.1",
    "argv": [
      "/usr/local/node-v22.13.1-linux-x64/bin/node",
      "/home/<USER>/Desktop/nso_v11/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 96411648,
      "heapTotal": 59191296,
      "heapUsed": 26651392,
      "external": 20396719,
      "arrayBuffers": 18262543
    }
  },
  "os": {
    "loadavg": [
      4.9,
      4.79,
      4.7
    ],
    "uptime": 170119.42
  },
  "trace": [
    {
      "column": 13,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/router/index.js",
      "function": "Function.use",
      "line": 469,
      "method": "use",
      "native": false
    },
    {
      "column": 21,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": null,
      "line": 227,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.forEach",
      "line": null,
      "method": "forEach",
      "native": false
    },
    {
      "column": 7,
      "file": "/home/<USER>/Desktop/nso_v11/backend/node_modules/express/lib/application.js",
      "function": "Function.use",
      "line": 224,
      "method": "use",
      "native": false
    },
    {
      "column": 5,
      "file": "/home/<USER>/Desktop/nso_v11/backend/server.js",
      "function": null,
      "line": 106,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1562,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Object..js",
      "line": 1699,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1313,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Function._load",
      "line": 1123,
      "method": "_load",
      "native": false
    },
    {
      "column": 14,
      "file": "node:diagnostics_channel",
      "function": "TracingChannel.traceSync",
      "line": 322,
      "method": "traceSync",
      "native": false
    }
  ]
}
