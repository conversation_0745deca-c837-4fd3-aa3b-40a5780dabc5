const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config');
const logger = require('../utils/logger');

/**
 * Authentication middleware
 * Verifies JWT token and attaches user info to request
 */
const auth = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. No valid token provided.',
        code: 'NO_TOKEN'
      });
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify token
    const decoded = jwt.verify(token, config.JWT_SECRET);
    
    // Get user from database
    const user = await User.findOne({ 
      userId: decoded.userId,
      'deviceInfo.deviceId': decoded.deviceId,
      isActive: true 
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. User not found or inactive.',
        code: 'USER_NOT_FOUND'
      });
    }
    
    // Check if device is activated
    if (!user.deviceInfo.isActivated) {
      return res.status(401).json({
        success: false,
        error: 'Device not activated.',
        code: 'DEVICE_NOT_ACTIVATED'
      });
    }
    
    // Attach user info to request
    req.user = {
      userId: user.userId,
      deviceId: user.deviceInfo.deviceId,
      role: user.role,
      facility: user.facility,
      fullName: user.fullName
    };
    
    // Extract session ID from headers if available
    req.sessionId = req.headers['x-session-id'];
    
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Access denied. Invalid token.',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Access denied. Token expired.',
        code: 'TOKEN_EXPIRED'
      });
    }
    
    logger.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during authentication.',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Optional authentication middleware
 * Attaches user info if token is valid, but doesn't require authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, config.JWT_SECRET);
      
      const user = await User.findOne({ 
        userId: decoded.userId,
        'deviceInfo.deviceId': decoded.deviceId,
        isActive: true 
      });
      
      if (user && user.deviceInfo.isActivated) {
        req.user = {
          userId: user.userId,
          deviceId: user.deviceInfo.deviceId,
          role: user.role,
          facility: user.facility,
          fullName: user.fullName
        };
        req.sessionId = req.headers['x-session-id'];
      }
    }
    
    next();
    
  } catch (error) {
    // Ignore auth errors in optional auth
    next();
  }
};

/**
 * Role-based authorization middleware
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Access denied. Authentication required.',
        code: 'AUTH_REQUIRED'
      });
    }
    
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Access denied. Insufficient permissions.',
        code: 'INSUFFICIENT_PERMISSIONS',
        requiredRoles: roles,
        userRole: req.user.role
      });
    }
    
    next();
  };
};

/**
 * Device validation middleware
 * Ensures request comes from a registered and activated device
 */
const validateDevice = async (req, res, next) => {
  try {
    const deviceId = req.headers['x-device-id'];
    
    if (!deviceId) {
      return res.status(400).json({
        success: false,
        error: 'Device ID required.',
        code: 'DEVICE_ID_REQUIRED'
      });
    }
    
    const user = await User.findOne({ 
      'deviceInfo.deviceId': deviceId,
      isActive: true 
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Device not registered.',
        code: 'DEVICE_NOT_REGISTERED'
      });
    }
    
    if (!user.deviceInfo.isActivated) {
      return res.status(403).json({
        success: false,
        error: 'Device not activated.',
        code: 'DEVICE_NOT_ACTIVATED'
      });
    }
    
    req.device = {
      deviceId: user.deviceInfo.deviceId,
      userId: user.userId,
      activatedAt: user.deviceInfo.activatedAt
    };
    
    next();
    
  } catch (error) {
    logger.error('Device validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during device validation.',
      code: 'DEVICE_VALIDATION_ERROR'
    });
  }
};

/**
 * Rate limiting per user
 */
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();
  
  return (req, res, next) => {
    if (!req.user) {
      return next();
    }
    
    const userId = req.user.userId;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get user's request history
    let requests = userRequests.get(userId) || [];
    
    // Remove old requests outside the window
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if user has exceeded the limit
    if (requests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        error: 'Too many requests. Please try again later.',
        code: 'USER_RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
    
    // Add current request
    requests.push(now);
    userRequests.set(userId, requests);
    
    next();
  };
};

/**
 * Generate JWT token for user
 */
const generateToken = (user) => {
  const payload = {
    userId: user.userId,
    deviceId: user.deviceInfo.deviceId,
    role: user.role,
    iat: Math.floor(Date.now() / 1000)
  };
  
  return jwt.sign(payload, config.JWT_SECRET, {
    expiresIn: config.JWT_EXPIRES_IN
  });
};

/**
 * Verify activation key
 */
const verifyActivationKey = (providedKey, storedKey) => {
  // In production, you might want to use more sophisticated key verification
  return providedKey === storedKey;
};

module.exports = {
  auth,
  optionalAuth,
  authorize,
  validateDevice,
  userRateLimit,
  generateToken,
  verifyActivationKey
};
