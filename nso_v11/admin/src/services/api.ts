import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';
const API_VERSION = '/api/v1';
const ADMIN_TOKEN = process.env.REACT_APP_ADMIN_TOKEN || 'admin-secret-token';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}${API_VERSION}`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'x-admin-token': ADMIN_TOKEN,
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.error('Unauthorized access - check admin token');
    }
    
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: string;
  userId: string;
  fullName: string;
  email: string;
  role: string;
  facility: string;
  state: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: string;
  deviceId: string;
  registeredAt: string;
  isActive: boolean;
  deviceInfo: {
    deviceId: string;
    activationKeyId: string;
    isActivated: boolean;
    activatedAt?: string;
    deviceModel?: string;
    osVersion?: string;
    appVersion?: string;
  };
  activationKeyInfo?: {
    keyId: string;
    shortCode: string;
    status: string;
    expiresAt: string;
    remainingDays: number;
    createdBy: {
      adminId: string;
      adminName: string;
      adminEmail: string;
    };
  };
}

export interface ActivationKey {
  keyId: string;
  activationKey: string;
  shortCode: string;
  userId: string;
  deviceId: string;
  role: string;
  facility: string;
  state: string;
  validityMonths: number;
  issuedAt: string;
  expiresAt: string;
  status: 'active' | 'used' | 'expired' | 'revoked';
  isUsed: boolean;
  usedAt?: string;
  createdBy: {
    adminId: string;
    adminName: string;
    adminEmail: string;
  };
  notes?: string;
  adminNotes?: string;
}

export interface CreateUserRequest {
  fullName: string;
  email: string;
  role: string;
  facility: string;
  state: string;
  contactInfo: string;
  deviceId: string;
  validityMonths: number;
  notes?: string;
}

export interface DashboardStats {
  users: {
    total: number;
    active: number;
    inactive: number;
  };
  activationKeys: {
    total: number;
    active: number;
    used: number;
    expired: number;
    revoked: number;
  };
  recent: {
    users: Array<{
      userId: string;
      fullName: string;
      role: string;
      facility: string;
      createdAt: string;
    }>;
    keys: Array<{
      keyId: string;
      shortCode: string;
      status: string;
      facility: string;
      createdAt: string;
      createdBy: {
        adminName: string;
      };
    }>;
  };
}

// API Service Class
class ApiService {
  // Users API
  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    role?: string;
    facility?: string;
    state?: string;
  } = {}) {
    const response = await apiClient.get('/admin/users', { params });
    return response.data;
  }

  async getUser(userId: string) {
    const response = await apiClient.get(`/admin/users/${userId}`);
    return response.data;
  }

  async createUser(userData: CreateUserRequest) {
    const response = await apiClient.post('/admin/users', userData);
    return response.data;
  }

  async updateUser(userId: string, userData: Partial<User>) {
    const response = await apiClient.put(`/admin/users/${userId}`, userData);
    return response.data;
  }

  async deactivateUser(userId: string) {
    const response = await apiClient.delete(`/admin/users/${userId}`);
    return response.data;
  }

  // Activation Keys API
  async getActivationKeys(params: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  } = {}) {
    const response = await apiClient.get('/admin/activation-keys', { params });
    return response.data;
  }

  async revokeActivationKey(keyId: string, reason?: string) {
    const response = await apiClient.post(`/admin/activation-keys/${keyId}/revoke`, { reason });
    return response.data;
  }

  // Dashboard API
  async getDashboardStats(): Promise<{ data: DashboardStats }> {
    const response = await apiClient.get('/admin/dashboard/stats');
    return response.data;
  }

  // Analytics API
  async getAnalytics(params: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  } = {}) {
    const response = await apiClient.get('/admin/analytics', { params });
    return response.data;
  }

  // Activity Logs API
  async getActivityLogs(params: {
    page?: number;
    limit?: number;
    userId?: string;
    activityType?: string;
    startDate?: string;
    endDate?: string;
  } = {}) {
    const response = await apiClient.get('/admin/activity', { params });
    return response.data;
  }

  // Sync Management API
  async getSyncStatus() {
    const response = await apiClient.get('/admin/sync/status');
    return response.data;
  }

  async triggerSync(syncType: string) {
    const response = await apiClient.post('/admin/sync/trigger', { syncType });
    return response.data;
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
