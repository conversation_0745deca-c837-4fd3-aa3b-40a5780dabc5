const mongoose = require('mongoose');

const userActivitySchema = new mongoose.Schema({
  // Activity Identification
  activityId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // User Information
  userId: {
    type: String,
    required: true,
    index: true
  },
  deviceId: {
    type: String,
    required: true,
    index: true
  },
  
  // Session Information
  sessionId: {
    type: String,
    required: true,
    index: true
  },
  
  // Activity Details
  activityType: {
    type: String,
    required: true,
    enum: [
      'app_launch',
      'app_close',
      'screen_view',
      'button_click',
      'form_submit',
      'search',
      'diagnosis_create',
      'diagnosis_view',
      'diagnosis_edit',
      'clinical_record_view',
      'clinical_record_create',
      'sync_initiated',
      'sync_completed',
      'sync_failed',
      'settings_change',
      'profile_update',
      'login',
      'logout',
      'error_occurred',
      'feature_used',
      'help_accessed',
      'export_data',
      'import_data'
    ]
  },
  
  // Screen/Feature Context
  screen: {
    name: String,
    route: String,
    category: String
  },
  
  // Action Details
  action: {
    name: String,
    target: String, // button id, form name, etc.
    value: mongoose.Schema.Types.Mixed,
    metadata: mongoose.Schema.Types.Mixed
  },
  
  // Clinical Context (if applicable)
  clinicalContext: {
    patientId: String,
    recordId: String,
    diagnosisId: String,
    category: String,
    severity: String
  },
  
  // Performance Metrics
  performance: {
    loadTime: Number, // milliseconds
    responseTime: Number, // milliseconds
    memoryUsage: Number, // bytes
    batteryLevel: Number, // percentage
    networkLatency: Number // milliseconds
  },
  
  // Device Context
  deviceContext: {
    platform: String, // ios, android, web
    osVersion: String,
    appVersion: String,
    deviceModel: String,
    screenResolution: String,
    orientation: {
      type: String,
      enum: ['portrait', 'landscape']
    },
    connectionType: String, // wifi, cellular, offline
    batteryLevel: Number,
    availableStorage: Number // bytes
  },
  
  // Location Context (if enabled)
  location: {
    facility: String,
    state: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    accuracy: Number
  },
  
  // Error Information (if activity is error-related)
  error: {
    code: String,
    message: String,
    stack: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    category: String,
    recoverable: Boolean
  },
  
  // User Interaction Data
  interaction: {
    inputMethod: String, // touch, voice, keyboard
    gestureType: String, // tap, swipe, pinch, etc.
    duration: Number, // milliseconds spent on action
    attempts: Number, // number of attempts for this action
    successful: Boolean
  },
  
  // Sync Context (if sync-related)
  syncContext: {
    syncId: String,
    dataType: String,
    recordCount: Number,
    dataSize: Number,
    syncDuration: Number,
    networkSpeed: Number
  },
  
  // Feature Usage
  featureUsage: {
    featureName: String,
    usageCount: Number,
    firstUse: Boolean,
    helpAccessed: Boolean,
    completed: Boolean
  },
  
  // Timing Information
  timestamp: {
    type: Date,
    default: Date.now
  },
  duration: Number, // milliseconds
  
  // Privacy & Compliance
  dataRetentionDays: {
    type: Number,
    default: 90
  },
  anonymized: {
    type: Boolean,
    default: false
  },
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: false // Using custom timestamp field
});

// Indexes
userActivitySchema.index({ userId: 1, timestamp: -1 });
userActivitySchema.index({ deviceId: 1, timestamp: -1 });
userActivitySchema.index({ sessionId: 1, timestamp: -1 });
userActivitySchema.index({ activityType: 1, timestamp: -1 });
userActivitySchema.index({ 'screen.name': 1, timestamp: -1 });
userActivitySchema.index({ 'clinicalContext.recordId': 1 });
userActivitySchema.index({ 'error.severity': 1, timestamp: -1 });
userActivitySchema.index({ timestamp: 1 }); // For TTL

// TTL Index for automatic cleanup
userActivitySchema.index(
  { createdAt: 1 }, 
  { expireAfterSeconds: 90 * 24 * 60 * 60 } // 90 days
);

// Methods
userActivitySchema.methods.anonymize = function() {
  this.userId = 'anonymous';
  this.deviceId = 'anonymous';
  this.anonymized = true;
  
  // Remove sensitive data
  if (this.clinicalContext) {
    this.clinicalContext.patientId = undefined;
    this.clinicalContext.recordId = undefined;
  }
  
  if (this.location && this.location.coordinates) {
    this.location.coordinates = undefined;
  }
  
  return this.save();
};

userActivitySchema.methods.isExpired = function() {
  const expiryDate = new Date(this.createdAt);
  expiryDate.setDate(expiryDate.getDate() + this.dataRetentionDays);
  return new Date() > expiryDate;
};

// Static methods
userActivitySchema.statics.findByUser = function(userId, limit = 100) {
  return this.find({ userId })
    .sort({ timestamp: -1 })
    .limit(limit);
};

userActivitySchema.statics.findBySession = function(sessionId) {
  return this.find({ sessionId })
    .sort({ timestamp: 1 });
};

userActivitySchema.statics.findByActivityType = function(activityType, limit = 100) {
  return this.find({ activityType })
    .sort({ timestamp: -1 })
    .limit(limit);
};

userActivitySchema.statics.getErrorActivities = function(severity = null) {
  const query = { activityType: 'error_occurred' };
  if (severity) {
    query['error.severity'] = severity;
  }
  
  return this.find(query)
    .sort({ timestamp: -1 });
};

userActivitySchema.statics.getUserStats = function(userId, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        userId,
        timestamp: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } },
          activityType: '$activityType'
        },
        count: { $sum: 1 },
        avgDuration: { $avg: '$duration' }
      }
    },
    {
      $sort: { '_id.date': -1 }
    }
  ]);
};

userActivitySchema.statics.getFeatureUsage = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        timestamp: { $gte: startDate },
        'featureUsage.featureName': { $exists: true }
      }
    },
    {
      $group: {
        _id: '$featureUsage.featureName',
        totalUsage: { $sum: '$featureUsage.usageCount' },
        uniqueUsers: { $addToSet: '$userId' },
        avgDuration: { $avg: '$duration' },
        firstTimeUsers: {
          $sum: { $cond: ['$featureUsage.firstUse', 1, 0] }
        }
      }
    },
    {
      $addFields: {
        uniqueUserCount: { $size: '$uniqueUsers' }
      }
    },
    {
      $sort: { totalUsage: -1 }
    }
  ]);
};

userActivitySchema.statics.getPerformanceMetrics = function(userId, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        userId,
        timestamp: { $gte: startDate },
        'performance.loadTime': { $exists: true }
      }
    },
    {
      $group: {
        _id: '$screen.name',
        avgLoadTime: { $avg: '$performance.loadTime' },
        avgResponseTime: { $avg: '$performance.responseTime' },
        avgMemoryUsage: { $avg: '$performance.memoryUsage' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { avgLoadTime: -1 }
    }
  ]);
};

module.exports = mongoose.model('UserActivity', userActivitySchema);
